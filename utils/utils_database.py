import os
import pandas as pd
import logger

from utils import utils_arguments as arg
from urllib import parse
from __init__ import path
from datetime import datetime, timedelta
from sqlalchemy import create_engine


class DatabaseConnection:
    def __init__(self, db_url):
        self.engine = create_engine(db_url)

    def __enter__(self):
        self.conn = self.engine.connect()
        return self.conn

    def __exit__(self, exc_type, exc_value, traceback):
        if self.conn:
            self.conn.close()

    def _generate_create_table_sql(self, data_df, table_name):
        # 生成 CREATE TABLE 语句，基于 DataFrame 的列名和数据类型
        # 获取DataFrame的列和数据类型
        columns = []
        for col, dtype in data_df.dtypes.items():
            # 将pandas数据类型映射到SQL数据类型
            if pd.api.types.is_integer_dtype(dtype):
                sql_type = "INTEGER"
            elif pd.api.types.is_float_dtype(dtype):
                sql_type = "FLOAT"
            elif pd.api.types.is_datetime64_any_dtype(dtype):
                sql_type = "TIMESTAMP"
            else:
                sql_type = "TEXT"
            columns.append(f"{col} {sql_type}")

        # 生成CREATE TABLE语句
        columns_sql = ", ".join(columns)
        create_table_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({columns_sql});"

        return create_table_sql

    def large_data_output_database(self, data_df, table_name):
        # 将大文件数据 data_df 导入 table_name 表中，数据库使用的是 PostgreSQL
        # 1. 添加时间戳
        data_df["insert_timestamp"] = datetime.now().strftime("%F %T")

        # 2. 动态创建表的 SQL 语句
        # 如果需要导入的数据为空，要记录到日志中
        if data_df.empty:
            logger.info(f"DataFrame为空，不输出数据到数据库: {table_name}")
            return

        # 创建表结构
        create_table_sql = self._generate_create_table_sql(data_df, table_name)
        with self.conn.engine.connect() as connection:
            connection.execute(create_table_sql)

        # 3. 先将 DataFrame 写入 CSV 文件，这里写入的格式是TSV格式，不是传统CSVge shi
        csv_file = f"{path}/cache/{table_name}.csv"
        data_df.to_csv(csv_file, index=False, sep="\t")

        # 4. 使用 COPY 命令将数据导入 PostgreSQL
        with self.conn.engine.raw_connection() as raw_conn:
            cursor = raw_conn.cursor()
            cursor.execute(
                f"""COPY {table_name} FROM '{csv_file}' DELIMITER '\t' CSV HEADER;
            """
            )
            raw_conn.commit()

        # 5. 删除临时 CSV 文件
        os.remove(csv_file)


def engine_conn(type_database):
    user = arg.conf(f"{type_database}_user")
    password = arg.conf(f"{type_database}_password")
    password = parse.quote_plus(
        password
    )  # 对数据库密码进行URL编码，确保密码中的特殊字符不会破坏链接字符串的格式
    host = arg.conf(f"{type_database}_host")
    port = arg.conf(f"{type_database}_port")
    database = arg.conf(f"{type_database}_database")
    database_dict = {
        "hive": "hive",
        "postgre": "postgresql",
        "oracle": "oracle",
        "mysql": "mysql+pymysql",
    }
    database_name = database_dict.get(f"{type_database}")
    user_password_host_port_database_str = f"{user}:{password}@{host}:{port}/{database}"

    if type_database == "hive":
        auth = arg.conf("hive_auth")
        db_url = f"{database_name}://{user_password_host_port_database_str}?auth={auth}"
    elif type_database in ["postgre", "oracle", "mysql"]:
        db_url = f"{database_name}://{user_password_host_port_database_str}"

    return DatabaseConnection(db_url)
