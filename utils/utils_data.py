import os
import pandas as pd
from datetime import datetime
from __init__ import path
from utils.utils_log import logger_config_base, logger_config_local
from utils import utils_database

log_filename = os.path.splitext(os.path.basename(__file__))[0]
logger = logger_config_local(f"{path}/log/{log_filename}.log")


def output_database(
    df, filename, chunksize=1000_000, if_exists="append", dtype=None, index=False
):
    if not df.empty:
        df["insert_timestamp"] = datetime.now().strftime("%F %T")
        logger.success("Writing to database started.")
        with utils_database.engine_conn("postgre") as conn:
            df.to_sql(
                filename,
                con=conn.engine,
                index=index,
                if_exists=if_exists,
                chunksize=chunksize,
                dtype=dtype,
            )
        logger.success("Writing to database conculusion-succeeded.")
    else:
        logger.info("Writing to database conculusion-failed.")


def re_get_row_data(rs):
    data_list = []
    while (rs.error_code == "0") & rs.next():
        data_list.append(rs.get_row_data())
    result = pd.DataFrame(data_list, columns=rs.fields)
    return result
