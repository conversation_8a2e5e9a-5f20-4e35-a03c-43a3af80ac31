import backtrader as bt
import pandas as pd
from datetime import datetime
import os


class BacktestRunner:
    def __init__(
        self,
        strategy_class,
        data_dir="/home/<USER>/Studios/600-codes/dataset/stock_data/stocks",
        cash=1000000.0,
        commission=0.0003,
    ):
        self.cerebro = bt.Cerebro()
        self.strategy_class = strategy_class
        self.data_dir = data_dir
        self.cash = cash
        self.commission = commission

    def load_stock_data(self, stock_codes, start_date=None, end_date=None):
        """加载股票数据"""
        for code in stock_codes:
            file_path = os.path.join(self.data_dir, code, "day.csv")
            if not os.path.exists(file_path):
                print(f"找不到股票{code}的数据文件")
                continue

            df = pd.read_csv(file_path)
            df["date"] = pd.to_datetime(df["date"])
            df.set_index("date", inplace=True)

            # 过滤日期范围
            if start_date:
                df = df[df.index >= start_date]
            if end_date:
                df = df[df.index <= end_date]

            # 处理停牌数据
            df = df[df["volume"] > 0]

            data = bt.feeds.PandasData(
                dataname=df,
                datetime=None,
                open="open",
                high="high",
                low="low",
                close="close",
                volume="volume",
                openinterest=-1,
                name=code,
            )

            self.cerebro.adddata(data)

    def run(self):
        """运行回测"""
        # 设置初始资金
        self.cerebro.broker.setcash(self.cash)

        # 设置手续费
        self.cerebro.broker.setcommission(commission=self.commission)

        # 添加分析器
        self.cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name="sharpe")
        self.cerebro.addanalyzer(bt.analyzers.DrawDown, _name="drawdown")
        self.cerebro.addanalyzer(bt.analyzers.Returns, _name="returns")
        self.cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name="trades")

        # 添加策略
        self.cerebro.addstrategy(self.strategy_class)

        # 运行回测
        print(f"初始资金: {self.cerebro.broker.getvalue():.2f}")
        results = self.cerebro.run()
        print(f"最终资金: {self.cerebro.broker.getvalue():.2f}")

        # 分析结果
        strat = results[0]
        self._print_analysis(strat)

        return results

    def plot(self):
        """绘制回测结果"""
        self.cerebro.plot(style="candlestick")

    def _print_analysis(self, strat):
        """打印分析结果"""
        # 夏普比率
        sharpe = strat.analyzers.sharpe.get_analysis()["sharperatio"]
        print(f"夏普比率: {sharpe:.2f}")

        # 最大回撤
        drawdown = strat.analyzers.drawdown.get_analysis()
        print(f"最大回撤: {drawdown.max.drawdown:.2f}%")

        # 收益率
        returns = strat.analyzers.returns.get_analysis()
        print(f"年化收益率: {returns.year:.2f}%")

        # 交易分析
        trades = strat.analyzers.trades.get_analysis()
        print(f"总交易次数: {trades.total.total}")
        if trades.won.total > 0:
            print(f"盈利交易次数: {trades.won.total}")
            print(f"平均盈利: {trades.won.pnl.average:.2f}")
        if trades.lost.total > 0:
            print(f"亏损交易次数: {trades.lost.total}")
            print(f"平均亏损: {trades.lost.pnl.average:.2f}")
