from utils.strategies.tdx_rotation import TDXRotationStrategy
from utils.backtest.runner import BacktestRunner


def run_tdx_backtest():
    # 初始化回测器
    runner = BacktestRunner(
        strategy_class=TDXRotationStrategy, cash=1000000.0, commission=0.0003
    )

    # 选择要回测的股票
    stock_codes = ["sh.600000", "sh.600036", "sh.601318", "sh.600519", "sh.600276"]

    # 加载数据
    runner.load_stock_data(
        stock_codes=stock_codes, start_date="2020-01-01", end_date="2023-12-31"
    )

    # 运行回测
    results = runner.run()

    # 绘制结果
    runner.plot()


if __name__ == "__main__":
    run_tdx_backtest()
