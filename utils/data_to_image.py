import numpy as np
import pandas as pd
from PIL import Image
import os
import duckdb
from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas
import matplotlib.pyplot as plt
from PIL import Image, ImageDraw
import io
import base64
import h5py


# 保留原有的函数
def normalize_data(data):
    """
    将数据标准化到[0, 1]范围
    """
    min_val = data.min()
    max_val = data.max()
    if min_val == max_val:
        return np.zeros_like(data)
    return (data - min_val) / (max_val - min_val)


def generate_ohlc_image(data, size=600):
    """
    生成OHLC图像
    """
    data = data.iloc[:40]  # 使用前40行数据

    img = Image.new("L", (size, size), color=0)

    y_scale = size * 4 // 5
    x_scale = size / len(data)

    for i, row in data.iterrows():
        x = int(i * x_scale)
        open_y = int(row["adj_open"] * y_scale)
        high_y = int(row["adj_high"] * y_scale)
        low_y = int(row["adj_low"] * y_scale)
        close_y = int(row["adj_close"] * y_scale)

        # 绘制从最低价到最高价的垂直线
        for y in range(low_y, high_y + 1):
            img.putpixel((x + 1, size - 1 - y), 255)

        # 绘制开盘价和收盘价的水平线
        img.putpixel((x, size - 1 - open_y), 255)
        img.putpixel((x + 2, size - 1 - close_y), 255)

        # 绘制成交量
        volume_height = int(row["adj_volume"] * (size // 5))
        for y in range(volume_height):
            img.putpixel((x + 1, size - 1 - y), 255)

    return img


def generate_and_save_images(stock_code_date, window_data, output_dir):
    """
    生成图像并保存

    参数:
    stock_code_date (str): 股票代码和日期的组合，格式为 'code_date'
    window_data (str): JSON格式的窗口数据
    output_dir (str): 输出图像的目录
    """
    stock_code, start_date = stock_code_date.split("_")
    df = pd.read_json(window_data, orient="records")

    # 标准化数据
    df["adj_open"] = normalize_data(df["open"])
    df["adj_high"] = normalize_data(df["high"])
    df["adj_low"] = normalize_data(df["low"])
    df["adj_close"] = normalize_data(df["close"])
    df["adj_volume"] = normalize_data(df["volume"])

    # 使用前40行生成图像
    image_data = df.iloc[:40]
    image = generate_ohlc_image(image_data)

    # 使用41-42行计算收益率和标签
    open_price = df.iloc[40]["open"]  # 第41天的开盘价
    future_high = df.iloc[40:42]["high"].max()  # 第41-42天的最高价

    ret2 = (future_high - open_price) / open_price * 100
    label_ret2 = 1 if ret2 > 5 else 0

    # 创建保存目录
    png_dir = os.path.join(output_dir, "png_images")
    npy_dir = os.path.join(output_dir, "npy_data")
    label_dir = os.path.join(output_dir, "labels")

    for directory in [png_dir, npy_dir, label_dir]:
        if not os.path.exists(directory):
            os.makedirs(directory)

    # 生成文件名
    file_name = stock_code_date

    # 保存为PNG格式
    png_path = os.path.join(png_dir, f"{file_name}.png")
    image.save(png_path)

    # 保存为NPY格式
    image_array = np.array(image)
    npy_path = os.path.join(npy_dir, f"{file_name}.npy")
    np.save(npy_path, image_array)

    # 保存标签
    label_path = os.path.join(label_dir, f"{file_name}_label.txt")
    with open(label_path, "w") as f:
        f.write(f"ret2:{label_ret2}")


def generate_and_save_enhanced_images(stock_code_date, window_data, output_dir):
    """
    生成增强的图像并保存

    参数:
    stock_code_date (str): 股票代码和日期的组合，格式为 'code_date'
    window_data (str): JSON格式的窗口数据
    output_dir (str): 输出图像的目录
    """
    stock_code, start_date = stock_code_date.split("_")
    data = pd.read_json(window_data, orient="records")

    # 检查数据是否至少有42天
    if len(data) < 42:
        print(f"警告: 数据少于42天，跳过处理。")
        return

    # 提取股票代码和日期用于文件名
    code = data["code"].iloc[0]
    end_date = data["date"].iloc[39]  # 第40天的日期
    file_name = f"{code}_{end_date}"

    # 只使用第21到第40个交易日的数据
    data_slice = data.iloc[20:40]

    # 创建一个白色背景的图像
    img = Image.new("RGB", (224, 224), color="white")
    draw = ImageDraw.Draw(img)

    # 计算有效绘图区域
    # plot_width = 220
    ohlc_height = 175
    volume_height = 38
    # gap_height = 7

    # 计算20日移动平均线
    ma20 = data["close"].rolling(window=20).mean().iloc[20:40]

    # 计算价格和成交量的缩放因子，同时考虑20日均线
    price_min = min(data_slice["low"].min(), ma20.min())
    price_max = max(data_slice["high"].max(), ma20.max())
    price_range = price_max - price_min
    volume_max = data_slice["volume"].max()

    for i in range(20):
        day_data = data_slice.iloc[i]
        x_start = 2 + i * 11

        # 绘制OHLC
        open_y = (
            2
            + ohlc_height
            - int((day_data["open"] - price_min) / price_range * ohlc_height)
        )
        high_y = (
            2
            + ohlc_height
            - int((day_data["high"] - price_min) / price_range * ohlc_height)
        )
        low_y = (
            2
            + ohlc_height
            - int((day_data["low"] - price_min) / price_range * ohlc_height)
        )
        close_y = (
            2
            + ohlc_height
            - int((day_data["close"] - price_min) / price_range * ohlc_height)
        )

        # 绘制最高价到最低价的线（中间的垂直线）
        draw.line([(x_start + 5, high_y), (x_start + 5, low_y)], fill="black", width=1)
        # 绘制开盘价线（左侧）
        draw.rectangle([(x_start + 1, open_y), (x_start + 5, open_y)], fill="black")
        # 绘制收盘价线（右侧）
        draw.rectangle([(x_start + 6, close_y), (x_start + 10, close_y)], fill="black")

        # 绘制成交量
        volume_height_scaled = int(day_data["volume"] / volume_max * volume_height)
        volume_y_start = 224 - 2 - volume_height_scaled
        draw.rectangle(
            [(x_start + 1, volume_y_start), (x_start + 9, 222)],
            fill="green" if day_data["close"] >= day_data["open"] else "red",
        )

    # 绘制20日移动平均线
    ma20_points = [
        (
            2 + i * 11 + 5,
            2
            + ohlc_height
            - int((ma20.iloc[i] - price_min) / price_range * ohlc_height),
        )
        for i in range(20)
    ]
    for i in range(1, len(ma20_points)):
        draw.line([ma20_points[i - 1], ma20_points[i]], fill="blue", width=1)

    # 使用41-42行计算收益率和标签
    open_price = data.iloc[40]["open"]  # 第41天的开盘价
    future_high = data.iloc[40:42]["high"].max()  # 第41-42天的最高价
    ret2 = (future_high - open_price) / open_price * 100
    label_ret2 = 1 if ret2 > 5 else 0

    # 创建保存目录
    png_dir = os.path.join(output_dir, "enhance_png_images")
    npy_dir = os.path.join(output_dir, "enhance_npy_data")
    hdf5_dir = os.path.join(output_dir, "enhance_hdf5_data")
    label_dir = os.path.join(output_dir, "enhance_labels")

    for directory in [png_dir, npy_dir, hdf5_dir, label_dir]:
        if not os.path.exists(directory):
            os.makedirs(directory)

    # 生成文件名
    file_name = stock_code_date

    # 保存为PNG格式
    # png_path = os.path.join(png_dir, f"{file_name}.png")
    # img.save(png_path)

    # 保存为NPY格式
    image_array = np.array(img)
    # npy_path = os.path.join(npy_dir, f"{file_name}.npy")
    # np.save(npy_path, image_array)

    # 保存为HDF5格式
    hdf5_path = os.path.join(hdf5_dir, f"{file_name}.h5")
    with h5py.File(hdf5_path, "w") as hdf5_file:
        hdf5_file.create_dataset("image", data=image_array)
        hdf5_file.create_dataset("label", data=label_ret2)

    # 保存标签
    # label_path = os.path.join(label_dir, f"{file_name}_label.txt")
    # with open(label_path, "w") as f:
    #     f.write(f"ret2:{label_ret2}")


if __name__ == "__main__":
    db_path = "stock_data.db"
    output_directory = "/home/<USER>/Studios/600-codes/dataset/test1"

    conn = duckdb.connect(db_path)
    cursor = conn.cursor()

    # 查询所有窗口数据
    cursor.execute("SELECT stock_code_date, window_data FROM stock_windows")

    for stock_code_date, window_data in cursor:
        generate_and_save_images(stock_code_date, window_data, output_directory)
        generate_and_save_enhanced_images(
            stock_code_date, window_data, output_directory
        )

    conn.close()

    print("所有图像生成完成。")
