import os
import lmdb
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from PIL import Image
import numpy as np
import io


class LMDBDataset(Dataset):
    def __init__(self, db_path, transform=None):
        self.db_path = db_path
        self.env = lmdb.open(
            db_path, readonly=True, lock=False, readahead=False, meminit=False
        )
        with self.env.begin(write=False) as txn:
            self.length = int(txn.get("num-samples".encode()))
        self.transform = transform

    def __len__(self):
        return self.length

    def __getitem__(self, index):
        with self.env.begin(write=False) as txn:
            img_bytes = txn.get(f"image-{index:08d}".encode())
            label = int(txn.get(f"label-{index:08d}".encode()))

        img = Image.open(io.BytesIO(img_bytes)).convert("RGB")

        if self.transform:
            img = self.transform(img)

        return img, label


def get_transforms(is_training=True):
    if is_training:
        return transforms.Compose(
            [
                transforms.RandomResizedCrop(224),
                transforms.RandomHorizontalFlip(),
                transforms.ToTensor(),
                transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225]),
            ]
        )
    else:
        return transforms.Compose(
            [
                transforms.Resize(256),
                transforms.CenterCrop(224),
                transforms.ToTensor(),
                transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225]),
            ]
        )


def create_dataloaders(config):
    train_transform = get_transforms(is_training=True)
    val_test_transform = get_transforms(is_training=False)

    train_dataset = LMDBDataset(
        os.path.join(config["data"]["data_dir"], "train"), transform=train_transform
    )
    val_dataset = LMDBDataset(
        os.path.join(config["data"]["data_dir"], "val"), transform=val_test_transform
    )
    test_dataset = LMDBDataset(
        os.path.join(config["data"]["data_dir"], "test"), transform=val_test_transform
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=config["data"]["batch_size"],
        shuffle=True,
        num_workers=config["data"]["num_workers"],
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["data"]["batch_size"],
        shuffle=False,
        num_workers=config["data"]["num_workers"],
    )
    test_loader = DataLoader(
        test_dataset,
        batch_size=config["data"]["batch_size"],
        shuffle=False,
        num_workers=config["data"]["num_workers"],
    )

    return train_loader, val_loader, test_loader
