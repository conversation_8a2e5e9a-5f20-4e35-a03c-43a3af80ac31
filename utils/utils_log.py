# -*- coding: utf-8 -*-

from loguru import logger


# local日志配置：
# - 输出到指定文件路径
# - 可配置日志级别(默认DEBUG)
# - 支持日志文件轮转(默认10MB)和保留时间(默认10天)
# - 日志格式包含时间、级别、消息、文件名和行号
def logger_config_local(
    file_path, level="DEBUG", rotation="10 MB", retention="10 days"
):
    logger.add(
        file_path,
        format="{time:YY-MM-DD HH:mm:ss} | {level} | {message} ({file}:{line})",
        level=level,
        rotation=rotation,
        retention=retention,
    )
    return logger


# base日志配置：
# - 输出到控制台
# - 固定INFO级别
# - 简化的日志格式，仅包含时间、级别和消息
def logger_config_base():
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="{time:YY-MM-DD HH:mm:ss} | {level} | {message}",
        level="INFO",
    )
    return logger
