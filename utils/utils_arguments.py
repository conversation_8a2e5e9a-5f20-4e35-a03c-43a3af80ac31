import configparser
from __init__ import path

config = configparser.ConfigParser()
config.read(
    f"{path}/configs/setting_global.txt"
)  # setting_global.txt 中配置的是数据库的信息


def get_conf(category, keyword, abnormal_out=""):
    conf_out = (
        config.get(category, keyword)
        if config.get(category, keyword) != ""
        else abnormal_out
    )
    return conf_out


def conf(word, *update):
    word = str(word)

    # 连接 hive 数据库
    if word in [
        "hive_user",
        "hive_password",
        "hive_host",
        "hive_port",
        "hive_database",
        "hive_auth",
    ]:
        return get_conf("hive_database", word)
    elif word in [
        "mysql_user",
        "mysql_password",
        "mysql_host",
        "mysql_port",
        "mysql_database",
    ]:
        return get_conf("mysql_database", word)
    elif word in [
        "postgre_user",
        "postgre_password",
        "postgre_host",
        "postgre_port",
        "postgre_database",
    ]:
        return get_conf("postgre_database", word)
    elif word in [
        "oracle_user",
        "oracle_password",
        "oracle_host",
        "oracle_port",
        "oracle_database",
    ]:
        return get_conf("oracle_database", word)
