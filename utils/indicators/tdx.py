import backtrader as bt


class TDXIndicator(bt.Indicator):
    """通达信指标组合

    计算过程:
    1. VAR5 = (2*C+H+L)/4
    2. VAR6 = LLV(LOW,N1)
    3. VAR7 = HHV(HIGH,N2)
    4. VAR8 = 100-100*(HHV(HIGH,RSI_PERIOD)-CLOSE)/(HHV(HIGH,RSI_PERIOD)-LLV(LOW,RSI_PERIOD))
    5. MW = EMA(VAR8,3)
    6. VAR9 = EMA(VAR8,7)
    7. M1 = EMA(VAR9,5)
    8. MGL = EMA((VAR5-VAR6)/(VAR7-VAR6)*100,5)
    9. TML = EMA(0.667*MGL(-1)+0.333*MGL,2)
    10. 信号线 = (MGL/TML-1)*100
    """

    lines = ("mgl", "tml", "sig")  # 定义指标线
    params = dict(
        n1=21,  # VAR6的周期 (N1: 默认21, 范围0-100)
        n2=8,  # VAR7的周期 (N2: 默认8, 范围0-100)
        period_high=75,  # HHV周期
        period_low=75,  # LLV周期
        rsi_period=14,  # RSI周期
    )

    def __init__(self):
        # 参数有效性检查
        if not 0 <= self.p.n1 <= 100:
            raise ValueError("N1参数必须在0-100范围内")
        if not 0 <= self.p.n2 <= 100:
            raise ValueError("N2参数必须在0-100范围内")

        # 计算VAR5: (2*C+H+L)/4
        self.var5 = (2.0 * self.data.close + self.data.high + self.data.low) / 4.0

        # 计算VAR6: LLV(LOW,N1)
        self.var6 = bt.indicators.Lowest(self.data.low, period=self.p.n1)

        # 计算VAR7: HHV(HIGH,N2)
        self.var7 = bt.indicators.Highest(self.data.high, period=self.p.n2)

        # 计算RSI相关
        high_lowest = bt.indicators.Lowest(self.data.low, period=self.p.rsi_period)
        high_highest = bt.indicators.Highest(self.data.high, period=self.p.rsi_period)
        self.var8 = 100 - 100 * (high_highest - self.data.close) / (
            high_highest - high_lowest
        )

        # 计算MW和M1
        self.mw = bt.indicators.EMA(self.var8, period=3)
        self.var9 = bt.indicators.EMA(self.var8, period=7)
        self.m1 = bt.indicators.EMA(self.var9, period=5)

        # 计算MGL和TML
        self.mgl = bt.indicators.EMA(
            (self.var5 - self.var6) / (self.var7 - self.var6) * 100, period=5
        )
        self.tml = bt.indicators.EMA(0.667 * self.mgl(-1) + 0.333 * self.mgl, period=2)

        # 计算信号线
        self.sig = (self.mgl / self.tml - 1.0) * 100
