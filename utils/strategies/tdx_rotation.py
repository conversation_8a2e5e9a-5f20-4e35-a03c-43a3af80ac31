import backtrader as bt
from ..indicators.tdx import TDXIndicator


class TDXRotationStrategy(bt.Strategy):
    """基于通达信指标的轮动策略"""

    params = dict(
        period=20,
        top_k=3,  # 持仓数量
        mgl_upper=60,  # MGL上轨
        mgl_lower=40,  # MGL下轨
        sig_threshold=0,  # 信号阈值
    )

    def __init__(self):
        self.inds = {}
        for data in self.datas:
            self.inds[data] = TDXIndicator(data)

        # 用于跟踪持仓
        self.holding = []
        # 用于记录买入时间
        self.buy_dates = {}

    def log(self, txt, dt=None):
        dt = dt or self.datas[0].datetime.date(0)
        print(f"{dt.isoformat()} {txt}")

    def next(self):
        # 1. 生成交易信号
        to_buy = []
        to_sell = []

        for data, ind in self.inds.items():
            # 检查数据是否有效（非停牌）
            if data.volume[0] <= 0:  # 成交量为0表示停牌
                if self.getposition(data).size > 0:
                    continue
                else:
                    continue

            # 买入条件：
            # 1. MGL上穿TML且MGL<40
            # 2. 信号线大于阈值
            buy_signal = (
                ind.mgl[-1] < ind.tml[-1]
                and ind.mgl[0] > ind.tml[0]
                and ind.mgl[0] < self.p.mgl_lower
                and ind.sig[0] > self.p.sig_threshold
                and data.volume[0] > 0
            )  # 确保非停牌

            # 卖出条件：
            # 1. TML上穿MGL且MGL>60
            # 2. 信号线小于阈值
            sell_signal = (
                ind.tml[-1] < ind.mgl[-1]
                and ind.tml[0] > ind.mgl[0]
                and ind.mgl[0] > self.p.mgl_upper
            ) or ind.sig[0] < -self.p.sig_threshold

            if buy_signal:
                to_buy.append(data)
            if sell_signal and self.getposition(data).size > 0:
                to_sell.append(data)

        # 2. 执行卖出操作
        for data in to_sell:
            self.close(data)
            self.holding.remove(data)
            self.log(f"卖出: {data._name}")

        # 3. 执行买入操作
        if to_buy and len(self.holding) < self.p.top_k:
            # 按信号强度排序
            to_buy.sort(key=lambda x: self.inds[x].sig[0], reverse=True)
            # 取前N个
            to_buy = to_buy[: self.p.top_k - len(self.holding)]

            # 计算每个标的的权重
            weight = 1.0 / (len(self.holding) + len(to_buy))

            # 买入新标的
            for data in to_buy:
                self.buy(
                    data, size=self.broker.getvalue() * weight * 0.95 / data.close[0]
                )
                self.holding.append(data)
                self.buy_dates[data] = len(self)
                self.log(f"买入: {data._name}, 权重: {weight:.2%}")

            # 调整现有持仓的权重
            for data in self.holding:
                if data not in to_buy:
                    self.order_target_percent(data, target=weight * 0.95)
