import pandas as pd
import numpy as np
from typing import Dict, List
from datetime import datetime, timedelta
import vectorbt as vbt
from strategy.base.strategy_base import StrategyBase


class BacktestEngine:
    """回测引擎"""

    def __init__(self, strategy: StrategyBase):
        """
        初始化回测引擎

        Parameters:
        -----------
        strategy : StrategyBase
            策略实例
        """
        self.strategy = strategy
        self.portfolio = None
        self.current_date = strategy.start_date
        self.available_cash = strategy.initial_capital

    def run(self) -> Dict:
        """
        运行回测

        Returns:
        --------
        Dict
            回测结果
        """
        # 获取交易日历
        calendar = pd.date_range(
            start=self.strategy.start_date, end=datetime.now(), freq="B"  # 工作日
        )

        # 遍历每个交易日
        for date in calendar:
            self.current_date = date

            # 检查是否需要卖出
            self._process_sell_signals()

            # 生成买入信号
            if len(self.strategy.positions) < self.strategy.max_positions:
                buy_signals = self.strategy.generate_signals(date)
                self._process_buy_signals(buy_signals)

        # 计算回测结果
        return self._calculate_results()

    def _process_sell_signals(self):
        """处理卖出信号"""
        # 复制持仓字典，避免在遍历时修改
        positions = self.strategy.positions.copy()

        for code, position in positions.items():
            # 计算持仓天数
            holding_days = (self.current_date - position["entry_date"]).days

            # 检查是否应该卖出
            if self.strategy.should_sell(code, holding_days):
                self._execute_sell(code)

    def _process_buy_signals(self, signals: List[str]):
        """
        处理买入信号

        Parameters:
        -----------
        signals : List[str]
            买入信号列表
        """
        # 获取历史数据
        history_data = self.strategy.load_history_data(signals)

        for code in signals:
            if len(self.strategy.positions) >= self.strategy.max_positions:
                break

            # 获取最新价格
            latest_price = history_data.loc[self.current_date, (code, "close")]

            # 计算仓位大小
            quantity = self.strategy.calculate_position_size(
                self.available_cash, latest_price
            )

            if quantity > 0:
                self._execute_buy(code, quantity, latest_price)

    def _execute_buy(self, code: str, quantity: int, price: float):
        """执行买入操作"""
        cost = quantity * price
        if cost <= self.available_cash:
            # 更新可用资金
            self.available_cash -= cost

            # 记录持仓
            self.strategy.positions[code] = {
                "quantity": quantity,
                "entry_price": price,
                "entry_date": self.current_date,
                "cost": cost,
            }

            # 记录交易
            self.strategy.trades.append(
                {
                    "code": code,
                    "direction": "buy",
                    "date": self.current_date,
                    "price": price,
                    "quantity": quantity,
                    "cost": cost,
                }
            )

            # 记录资金流水
            self.strategy.cash_flows.append(
                {
                    "date": self.current_date,
                    "amount": -cost,
                    "type": "buy",
                    "code": code,
                }
            )

    def _execute_sell(self, code: str):
        """执行卖出操作"""
        position = self.strategy.positions[code]
        # 获取最新价格
        history_data = self.strategy.load_history_data([code])
        latest_price = history_data.loc[self.current_date, (code, "close")]

        # 计算收益
        revenue = position["quantity"] * latest_price
        profit = revenue - position["cost"]

        # 更新可用资金
        self.available_cash += revenue

        # 记录交易
        self.strategy.trades.append(
            {
                "code": code,
                "direction": "sell",
                "date": self.current_date,
                "price": latest_price,
                "quantity": position["quantity"],
                "revenue": revenue,
                "profit": profit,
            }
        )

        # 记录资金流水
        self.strategy.cash_flows.append(
            {"date": self.current_date, "amount": revenue, "type": "sell", "code": code}
        )

        # 移除持仓
        del self.strategy.positions[code]

    def _calculate_results(self) -> Dict:
        """计算回测结果"""
        # 转换交易记录为DataFrame
        trades_df = pd.DataFrame(self.strategy.trades)
        cash_flows_df = pd.DataFrame(self.strategy.cash_flows)

        if trades_df.empty:
            return {"error": "No trades executed during backtest period"}

        # 计算每日净值
        daily_equity = self._calculate_daily_equity(cash_flows_df)

        # 使用vectorbt计算绩效指标
        portfolio = vbt.Portfolio.from_orders(
            close=daily_equity,
            size=1.0,
            init_cash=self.strategy.initial_capital,
            freq="1D",
        )

        # 返回回测结果
        return {
            "trades": trades_df,
            "cash_flows": cash_flows_df,
            "daily_equity": daily_equity,
            "metrics": {
                "net_value": portfolio.final_value(),
                "annual_return": portfolio.annual_return(),
                "sharpe_ratio": portfolio.sharpe_ratio(),
                "max_drawdown": portfolio.max_drawdown(),
                "win_rate": portfolio.win_rate(),
                "profit_factor": portfolio.profit_factor(),
                "calmar_ratio": portfolio.calmar_ratio(),
                "sqn": portfolio.sqn(),
                "total_trades": len(trades_df),
                "total_profit": (
                    trades_df["profit"].sum() if "profit" in trades_df else 0
                ),
            },
        }

    def _calculate_daily_equity(self, cash_flows_df: pd.DataFrame) -> pd.Series:
        """计算每日净值"""
        if cash_flows_df.empty:
            return pd.Series()

        # 获取日期范围
        date_range = pd.date_range(
            start=cash_flows_df["date"].min(), end=cash_flows_df["date"].max(), freq="B"
        )

        # 计算每日资金变化
        daily_changes = cash_flows_df.groupby("date")["amount"].sum()

        # 计算累计净值
        equity = pd.Series(index=date_range, dtype=float)
        equity.iloc[0] = self.strategy.initial_capital

        for i in range(1, len(date_range)):
            date = date_range[i]
            prev_equity = equity.iloc[i - 1]
            daily_change = daily_changes.get(date, 0)
            equity.iloc[i] = prev_equity + daily_change

        return equity
