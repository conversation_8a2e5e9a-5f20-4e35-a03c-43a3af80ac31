import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import List, Dict, Union
from datetime import datetime, date


class StrategyBase(ABC):
    """策略基类"""

    def __init__(
        self,
        data_dir: str,
        start_date: Union[str, datetime, date] = "2022-01-01",
        initial_capital: float = 1000000.0,
        max_positions: int = 10,
        max_holding_days: int = 2,
    ):
        """
        初始化策略基类

        Parameters:
        -----------
        data_dir : str
            数据目录路径
        start_date : Union[str, datetime, date]
            回测起始日期
        initial_capital : float
            初始资金
        max_positions : int
            最大持仓数量
        max_holding_days : int
            最大持仓天数
        """
        self.data_dir = data_dir
        self.start_date = pd.to_datetime(start_date)
        self.initial_capital = initial_capital
        self.max_positions = max_positions
        self.max_holding_days = max_holding_days

        # 当前持仓
        self.positions: Dict[str, Dict] = {}
        # 历史交易记录
        self.trades: List[Dict] = []
        # 资金流水
        self.cash_flows: List[Dict] = []

    @abstractmethod
    def generate_signals(self, date: datetime) -> List[str]:
        """
        生成交易信号

        Parameters:
        -----------
        date : datetime
            当前交易日期

        Returns:
        --------
        List[str]
            建议买入的股票代码列表
        """
        pass

    @abstractmethod
    def should_sell(self, code: str, holding_days: int) -> bool:
        """
        判断是否应该卖出

        Parameters:
        -----------
        code : str
            股票代码
        holding_days : int
            持仓天数

        Returns:
        --------
        bool
            是否应该卖出
        """
        pass

    def calculate_position_size(self, available_cash: float, price: float) -> int:
        """
        计算仓位大小

        Parameters:
        -----------
        available_cash : float
            可用资金
        price : float
            股票价格

        Returns:
        --------
        int
            建议买入数量
        """
        # 默认每个仓位使用等额资金
        target_position_value = available_cash / (
            self.max_positions - len(self.positions)
        )
        # 计算可买数量，向下取整到100股
        quantity = int((target_position_value / price) // 100 * 100)
        return quantity

    @abstractmethod
    def load_history_data(
        self, codes: List[str], lookback_days: int = 60
    ) -> pd.DataFrame:
        """
        加载历史数据

        Parameters:
        -----------
        codes : List[str]
            股票代码列表
        lookback_days : int
            回看天数

        Returns:
        --------
        pd.DataFrame
            历史数据
        """
        pass

    def get_performance_metrics(self) -> Dict:
        """
        计算策略绩效指标

        Returns:
        --------
        Dict
            绩效指标字典
        """
        # TODO: 实现绩效指标计算
        return {
            "net_value": 0.0,
            "annual_return": 0.0,
            "win_rate": 0.0,
            "profit_loss_ratio": 0.0,
            "sharpe_ratio": 0.0,
            "current_drawdown": 0.0,
            "max_drawdown": 0.0,
            "calmar_ratio": 0.0,
            "total_trades": len(self.trades),
            "sqn": 0.0,
            "vwr": 0.0,
        }
