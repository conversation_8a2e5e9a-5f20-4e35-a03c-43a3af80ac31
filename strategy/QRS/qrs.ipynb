{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "import vectorbt as vbt\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 读取数据\n", "data_dir = \"/home/<USER>/Studios/600-codes/dataset/trade_data/stocks\"\n", "stock_code = \"sz.002564\"\n", "daily: pd.DataFrame = pd.read_csv(os.path.join(data_dir, stock_code, \"day.csv\"))\n", "\n", "pivot_table: pd.DataFrame = daily[[\"date\", \"high\", \"low\", \"open\", \"close\"]].set_index(\"date\")\n", "\n", "# 保存pivot_table到CSV文件\n", "pivot_table.to_csv(f\"{stock_code}_pivot_table.csv\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 从baostock下载的数据已经用前值填充将OHLC的数据进行了回填，成交量和成交额为0代表停牌\n", "high_df: pd.DataFrame = pivot_table[\"high\"]\n", "low_df: pd.DataFrame = pivot_table[\"low\"]\n", "close_df:pd.DataFrame = pivot_table[\"close\"]\n", "\n", "# t日信号,t+1日买入,则t日收益为(t+2/t+1)-1,所有shift=-8\n", "forward_returns:pd.DataFrame = close_df.pct_change(10).shift(-8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "strategy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 2}