import pandas as pd
import numpy as np
import vectorbt as vbt
import os
from datetime import datetime
from scipy import stats
import matplotlib.pyplot as plt


class QRSTimingStrategy:
    def __init__(self, data_dir):
        """
        初始化QRS择时策略
        :param data_dir: 股票数据目录
        """
        self.data_dir = data_dir

    def clean_data(self, df):
        """
        清洗数据，处理停牌等异常情况
        """
        # 去除停牌日和异常数据
        mask = (
            (df["volume"] > 0)  # 成交量大于0
            & (df["amount"] > 0)  # 成交额大于0
            & (df["open"] > 0)  # 开盘价大于0
            & (df["high"] > 0)  # 最高价大于0
            & (df["low"] > 0)  # 最低价大于0
            & (df["close"] > 0)  # 收盘价大于0
        )

        # 获取有效数据
        valid_data = df[mask].copy()

        # 对于停牌日，使用前一个交易日的收盘价填充
        df_filled = df.copy()
        df_filled.loc[~mask, ["open", "high", "low", "close"]] = np.nan
        df_filled[["open", "high", "low", "close"]] = df_filled[
            ["open", "high", "low", "close"]
        ].ffill()

        # 将成交量和成交额在停牌日设置为0
        df_filled.loc[~mask, ["volume", "amount"]] = 0

        return df_filled, valid_data, mask

    def load_stock_data(self, stock_code, start_date="2015-01-01", end_date=None):
        """
        加载股票数据
        """
        file_path = os.path.join(self.data_dir, stock_code, "day.csv")
        df = pd.read_csv(file_path)
        df["date"] = pd.to_datetime(df["date"])
        df.set_index("date", inplace=True)

        # 过滤日期范围
        mask = df.index >= start_date
        if end_date:
            mask &= df.index <= end_date
        df = df[mask]

        # 清洗数据
        df_filled, valid_data, valid_mask = self.clean_data(df)

        return df_filled, valid_data, valid_mask

    def calculate_qrs_signal(self, df, valid_mask, N=18, M=600, n_std=2):
        """
        计算QRS择时信号
        N: 计算beta的窗口大小
        M: 计算zscore的窗口大小
        n_std: zscore的标准差倍数
        """
        signals = pd.DataFrame(index=df.index)

        # 用于存储beta值
        betas = []
        r2s = []

        # 只在有效数据上计算信号
        valid_indices = np.where(valid_mask)[0]

        for i in range(N - 1, len(valid_indices)):
            # 获取窗口数据的索引
            window_indices = valid_indices[i - N + 1 : i + 1]

            # 确保窗口数据连续
            if len(window_indices) == N and (
                window_indices[-1] - window_indices[0]
            ) == (N - 1):
                # 获取窗口数据
                high = df["high"].iloc[window_indices].values
                low = df["low"].iloc[window_indices].values

                # 线性回归
                slope, intercept, r_value, p_value, std_err = stats.linregress(
                    low, high
                )

                betas.append(slope)
                r2s.append(r_value**2)
            else:
                betas.append(np.nan)
                r2s.append(np.nan)

        # 将beta值和R2值转换为Series
        beta_series = pd.Series(betas, index=df.index[valid_indices[N - 1 :]])
        r2_series = pd.Series(r2s, index=df.index[valid_indices[N - 1 :]])

        # 填充信号数据框
        signals["beta"] = np.nan
        signals["r2"] = np.nan
        signals.loc[beta_series.index, "beta"] = beta_series
        signals.loc[r2_series.index, "r2"] = r2_series

        # 计算beta的zscore（只在有效数据上计算）
        valid_beta = signals["beta"].dropna()
        if len(valid_beta) > M:
            rolling_mean = valid_beta.rolling(window=M).mean()
            rolling_std = valid_beta.rolling(window=M).std()
            zscore_beta = (valid_beta - rolling_mean) / rolling_std
            signals.loc[zscore_beta.index, "zscore_beta"] = zscore_beta

        # QRS信号 = zscore(beta) * R2
        signals["qrs_signal"] = signals["zscore_beta"] * signals["r2"]

        return signals

    def generate_signals(self, df, valid_mask, N=18, M=600, threshold=0.7):
        """
        生成交易信号
        """
        # 计算QRS信号
        signals = self.calculate_qrs_signal(df, valid_mask, N, M)

        # 生成交易信号（只在非停牌日生成信号）
        buy_signal = pd.Series(False, index=df.index)
        sell_signal = pd.Series(False, index=df.index)

        # 在有效数据上生成信号
        valid_signal = signals["qrs_signal"].dropna()
        buy_signal[valid_signal.index] = (valid_signal.shift(1) < threshold) & (
            valid_signal >= threshold
        )
        sell_signal[valid_signal.index] = (valid_signal.shift(1) > -threshold) & (
            valid_signal <= -threshold
        )

        # 在停牌日强制设置为False
        buy_signal[~valid_mask] = False
        sell_signal[~valid_mask] = False

        return buy_signal, sell_signal, signals

    def calculate_win_rate(self, pf):
        """
        计算策略胜率
        """
        if len(pf.trades) == 0:
            return 0.0

        # 获取所有交易的收益率
        trade_returns = pf.trades.returns

        # 计算盈利交易的数量
        winning_trades = (trade_returns > 0).sum()

        # 计算胜率
        win_rate = winning_trades / len(trade_returns)

        return win_rate

    def backtest(
        self,
        stock_code,
        start_date="2015-01-01",
        end_date=None,
        N=18,
        M=600,
        threshold=0.7,
    ):
        """
        执行回测
        """
        # 1. 加载数据
        df_filled, valid_data, valid_mask = self.load_stock_data(
            stock_code, start_date, end_date
        )
        print(f"\n数据加载情况:")
        print(f"总交易日数: {len(df_filled)}")
        print(f"有效交易日数: {valid_mask.sum()}")

        # 2. 生成信号
        entries, exits, signals = self.generate_signals(
            df_filled, valid_mask, N, M, threshold
        )
        print(f"\n信号生成情况:")
        print(f"买入信号数量: {entries.sum()}")
        print(f"卖出信号数量: {exits.sum()}")

        # 3. 使用vectorbt进行回测
        price = df_filled["close"]
        print(f"\n价格数据情况:")
        print(f"起始价格: {price.iloc[0]:.2f}")
        print(f"结束价格: {price.iloc[-1]:.2f}")
        print(f"价格范围: {price.min():.2f} - {price.max():.2f}")

        # 创建投资组合
        pf = vbt.Portfolio.from_signals(
            price,
            entries,
            exits,
            init_cash=1000000,  # 初始资金100万
            fees=0.0003,  # 手续费0.03%
            freq="1D",  # 日线级别
            size=np.where(valid_mask, 1, 0),  # 在停牌日不允许交易
            accumulate=False,  # 不累积仓位
        )

        # 打印持仓信息
        print(f"\n持仓情况:")
        trades = pf.trades
        print(f"总持仓次数: {len(trades)}")
        if len(trades) > 0:
            print(f"平均持仓时间: {trades.duration.mean():.2f}天")
            print(f"平均收益率: {trades.returns.mean():.2%}")
            print(f"累计收益率: {pf.total_return():.2%}")

            # 获取所有交易记录
            print(f"\n交易统计:")
            print(f"盈利交易数: {(trades.returns > 0).sum()}")
            print(f"亏损交易数: {(trades.returns <= 0).sum()}")
            print(f"最大单笔收益: {trades.returns.max():.2%}")
            print(f"最大单笔亏损: {trades.returns.min():.2%}")
            print(f"平均持仓天数: {trades.duration.mean():.2f}")

            # 计算年化收益率
            try:
                # 获取交易记录
                trades_records = []
                for i in range(len(trades.values)):
                    entry_idx = int(trades.values["entry_idx"][i])
                    exit_idx = int(trades.values["exit_idx"][i])

                    trades_records.append(
                        {
                            "开仓日期": price.index[entry_idx],
                            "平仓日期": price.index[exit_idx],
                            "持仓天数": trades.values["duration"][i],
                            "开仓价格": trades.values["entry_price"][i],
                            "平仓价格": trades.values["exit_price"][i],
                            "收益率": trades.values["return"][i],
                            "收益金额": trades.values["pnl"][i],
                        }
                    )

                trades_df = pd.DataFrame(trades_records)

                # 计算年化收益率
                first_date = trades_df["开仓日期"].min()
                last_date = trades_df["平仓日期"].max()
                total_days = (last_date - first_date).days

                if total_days > 0:
                    annual_return = (1 + pf.total_return()) ** (365 / total_days) - 1
                else:
                    annual_return = 0
                print(f"年化收益率: {annual_return:.2%}")

                print("\n前5笔交易:")
                print(trades_df.head().to_string())
                trades_df.to_csv(f"{stock_code}_trades.csv", index=False)

            except Exception as e:
                print(f"计算年化收益率和交易记录时出错: {str(e)}")

        return pf, signals, trades_records if len(trades) > 0 else None

    def batch_backtest(
        self,
        stock_codes,
        start_date="2015-01-01",
        end_date=None,
        N=18,
        M=600,
        threshold=0.7,
    ):
        """
        批量回测多个股票
        """
        results = {}
        for stock_code in stock_codes:
            try:
                pf, signals, trades_records = self.backtest(
                    stock_code, start_date, end_date, N, M, threshold
                )

                # 收集回测结果
                results[stock_code] = {
                    "total_return": pf.total_return(),
                    "sharpe_ratio": pf.sharpe_ratio(),
                    "max_drawdown": pf.max_drawdown(),
                    "win_rate": self.calculate_win_rate(pf),
                    "trades": len(pf.trades),
                    "avg_trade_return": (
                        pf.trades.returns.mean() if len(pf.trades) > 0 else 0.0
                    ),
                    "valid_days_ratio": signals["qrs_signal"]
                    .notna()
                    .mean(),  # 有效交易日比例
                }
            except Exception as e:
                print(f"处理 {stock_code} 时出错: {str(e)}")
                continue

        return pd.DataFrame.from_dict(results, orient="index")

    def calculate_trade_stats(self, pf):
        """
        计算交易统计数据
        """
        if len(pf.trades) == 0:
            return {
                "trade_count": 0,
                "win_rate": 0.0,
                "max_profit": 0.0,
                "max_loss": 0.0,
                "avg_hold_days": 0.0,
                "annual_return": 0.0,
                "trade_records": pd.DataFrame(),
            }

        # 获取交易记录
        trades = pf.trades

        # 计算持仓时间
        hold_days = [
            (exit_time - entry_time).days
            for entry_time, exit_time in zip(trades.entry_time, trades.exit_time)
        ]

        # 计算交易统计
        trade_returns = trades.returns
        winning_trades = (trade_returns > 0).sum()
        win_rate = winning_trades / len(trade_returns)

        # 计算最大盈亏
        max_profit = trade_returns.max() * 100
        max_loss = trade_returns.min() * 100

        # 计算平均持仓天数
        avg_hold_days = np.mean(hold_days)

        # 计算年化收益率
        total_days = (trades.exit_time.iloc[-1] - trades.entry_time.iloc[0]).days
        if total_days > 0:
            annual_return = ((1 + trade_returns.sum()) ** (365 / total_days) - 1) * 100
        else:
            annual_return = 0.0

        # 构建交易记录DataFrame
        trade_records = pd.DataFrame(
            {
                "entry_time": trades.entry_time,
                "exit_time": trades.exit_time,
                "entry_price": trades.entry_price,
                "exit_price": trades.exit_price,
                "size": trades.size,
                "pnl": trades.pnl,
                "return": trades.returns,
                "hold_days": hold_days,
            }
        )

        return {
            "trade_count": len(trades),
            "win_rate": win_rate * 100,
            "max_profit": max_profit,
            "max_loss": max_loss,
            "avg_hold_days": avg_hold_days,
            "annual_return": annual_return,
            "trade_records": trade_records,
        }


def main():
    # 示例使用
    data_dir = "/home/<USER>/Studios/600-codes/dataset/trade_data/stocks"
    strategy = QRSTimingStrategy(data_dir)

    # 测试单个股票
    stock_code = "sh.601318"  # 测试股票代码
    pf, signals, trades_records = strategy.backtest(
        stock_code,
        start_date="2015-01-01",
        N=18,  # beta计算窗口
        M=600,  # zscore计算窗口
        threshold=0.7,  # 信号阈值
    )

    # 计算权益曲线
    returns = pd.Series(pf.trades.returns.values, index=pf.trades.exit_time)
    equity = (1 + returns).cumprod()
    equity_series = pd.Series(1.0, index=pf.close.index)
    equity_series.loc[equity.index] = equity
    equity_series = equity_series.fillna(method="ffill")
    equity_series = equity_series.fillna(1.0)  # 填充开始时的空值

    # 保存详细的信号和持仓数据
    result_df = pd.DataFrame(
        {
            "price": pf.close,
            "positions": pf.positions.values,  # 使用.values获取底层数据
            "equity": equity_series,
            "qrs_signal": signals["qrs_signal"],
            "beta": signals["beta"],
            "r2": signals["r2"],
            "zscore_beta": signals["zscore_beta"],
        }
    )
    result_df.to_csv(f"{stock_code}_qrs_signals.csv")

    # 绘制回测图表
    pf.plot()

    # 获取回测结果
    stats = strategy.calculate_trade_stats(pf)

    print("\n持仓情况:")
    print(f"总持仓次数: {stats['trade_count']}")
    print(f"平均持仓时间: {stats['avg_hold_days']:.2f}天")
    print(f"平均收益率: {stats['win_rate']:.2f}%")
    print(f"累计收益率: {stats['annual_return']:.2f}%")

    print("\n交易统计:")
    print(f"盈利交易数: {(stats['trade_records']['return'] > 0).sum()}")
    print(f"亏损交易数: {(stats['trade_records']['return'] <= 0).sum()}")
    print(f"最大单笔收益: {stats['max_profit']:.2f}%")
    print(f"最大单笔亏损: {stats['max_loss']:.2f}%")
    print(f"平均持仓天数: {stats['avg_hold_days']:.2f}")

    # 保存交易记录
    stats["trade_records"].to_csv("trade_records.csv", index=False)

    # 绘制权益曲线
    plt.figure(figsize=(12, 6))
    plt.plot(equity_series.index, equity_series.values)
    plt.title("策略权益曲线")
    plt.xlabel("时间")
    plt.ylabel("净值")
    plt.grid(True)
    plt.show()


if __name__ == "__main__":
    main()
