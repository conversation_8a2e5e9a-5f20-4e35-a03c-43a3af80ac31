import pandas as pd
import os
from qrs_timing_strategy import QRSTimingStrategy
import matplotlib.pyplot as plt


def get_stock_codes(data_dir):
    """
    获取数据目录下的所有股票代码
    """
    return [d for d in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, d))]


def main():
    # 配置参数
    data_dir = "/home/<USER>/Studios/600-codes/dataset/trade_data/stocks"
    start_date = "2015-01-01"
    end_date = "2024-12-09"

    # 初始化策略
    strategy = QRSTimingStrategy(data_dir)

    # 获取股票列表
    stock_codes = get_stock_codes(data_dir)
    print(f"找到 {len(stock_codes)} 只股票")

    # 执行批量回测
    results = strategy.batch_backtest(
        stock_codes=stock_codes,
        start_date=start_date,
        end_date=end_date,
        mgl_upper=60,  # MGL上轨
        mgl_lower=40,  # MGL下轨
        sig_threshold=0,  # 信号阈值
    )

    # 保存回测结果
    results.to_csv("qrs_backtest_results.csv")

    # 打印汇总统计
    print("\n回测结果汇总:")
    print("平均收益率:", results["total_return"].mean())
    print("平均夏普比率:", results["sharpe_ratio"].mean())
    print("平均最大回撤:", results["max_drawdown"].mean())
    print("平均胜率:", results["win_rate"].mean())
    print("平均交易次数:", results["trades"].mean())

    # 绘制收益分布图
    plt.figure(figsize=(12, 6))
    plt.hist(results["total_return"], bins=50)
    plt.title("策略收益分布")
    plt.xlabel("收益率")
    plt.ylabel("股票数量")
    plt.savefig("returns_distribution.png")
    plt.close()

    # 输出表现最好的10只股票
    print("\n表现最好的10只股票:")
    print(results.sort_values("total_return", ascending=False).head(10))


if __name__ == "__main__":
    main()
