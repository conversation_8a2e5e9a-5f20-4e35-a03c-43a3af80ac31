import pandas as pd
import numpy as np
import os
from datetime import datetime, date
from pathlib import Path


class LocalDataLoader:
    """本地数据加载器"""

    def __init__(self, data_dir: str):
        """初始化数据加载器

        参数:
            data_dir: 数据目录路径
        """
        self.data_dir = data_dir
        self.stock_data_dir = os.path.join(data_dir, "stock")
        self.index_data_dir = os.path.join(data_dir, "index")

    def load_stock_daily(self, code: str) -> pd.DataFrame:
        """加载股票日线数据

        参数:
            code: 股票代码或指数代码

        返回:
            DataFrame: 包含日线数据的DataFrame
        """
        # 判断是否为指数
        if code.startswith(("sh.", "sz.")):
            file_path = os.path.join(self.index_data_dir, f"{code}.csv")
        else:
            file_path = os.path.join(self.stock_data_dir, code, "day.csv")

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"找不到数据文件: {file_path}")

        try:
            df = pd.read_csv(
                filepath_or_buffer=file_path,
                parse_dates=["date"],
                dtype={
                    "open": np.float64,
                    "high": np.float64,
                    "low": np.float64,
                    "close": np.float64,
                    "volume": np.float64,
                    "amount": np.float64,
                },
            )

            # 设置日期索引
            df.set_index("date", inplace=True)
            df.sort_index(inplace=True)

            return df

        except Exception as e:
            print(f"加载数据文件 {file_path} 时出错: {str(e)}")
            raise e

    def get_stock_list(self) -> list:
        """获取可用的股票列表"""
        stocks = []

        # 获取股票列表
        if os.path.exists(self.stock_data_dir):
            for item in os.listdir(self.stock_data_dir):
                if os.path.isdir(os.path.join(self.stock_data_dir, item)):
                    stocks.append(item)

        # 获取指数列表
        if os.path.exists(self.index_data_dir):
            for item in os.listdir(self.index_data_dir):
                if item.endswith(".csv"):
                    stocks.append(item[:-4])  # 移除.csv后缀

        return stocks

    def load_stock_data_between_dates(
        self, code: str, start_date: date, end_date: date
    ) -> pd.DataFrame:
        """加载指定日期范围内的股票数据

        参数:
            code: 股票代码或指数代码
            start_date: 开始日期
            end_date: 结束日期

        返回:
            DataFrame: 指定日期范围内的数据
        """
        df = self.load_stock_daily(code)
        mask = (df.index.date >= start_date) & (df.index.date <= end_date)
        return df[mask]
