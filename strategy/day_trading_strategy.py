import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from .data_loader import LocalDataLoader


class DayTradingStrategy:
    """日内交易策略"""

    def __init__(
        self,
        start_date,
        end_date,
        data_dir,  # 添加数据目录参数
        max_positions=10,  # 最大持仓数量
        stock_pool_size=5,  # 每日选股数量
        stop_loss=-0.02,  # 止损比例
        stop_profit=0.05,  # 止盈比例
        initial_capital=1000000,  # 初始资金
    ):
        """初始化策略

        参数:
            start_date: 回测开始日期
            end_date: 回测结束日期
            data_dir: 数据目录路径
            max_positions: 最大持仓数量
            stock_pool_size: 每日选股数量
            stop_loss: 止损比例
            stop_profit: 止盈比例
            initial_capital: 初始资金
        """
        self.start_date = start_date
        self.end_date = end_date
        self.data_dir = data_dir
        self.max_positions = max_positions
        self.stock_pool_size = stock_pool_size
        self.stop_loss = stop_loss
        self.stop_profit = stop_profit
        self.initial_capital = initial_capital

        # 初始化数据加载器
        self.data_loader = LocalDataLoader(data_dir)

        # 初始化数据
        self.all_stocks_data = {}  # 所有股票的数据
        self.daily_stock_pool = {}  # 每日选股池
        self.positions = {}  # 当前持仓
        self.trades = []  # 交易记录
        self.cash_flows = []  # 资金流水
        self.nav_series = None  # 净值序列
        self.cash = initial_capital  # 当前现金

    def load_data(self):
        """加载数据"""
        print("正在加载本地数据...")
        # 获取股票列表
        stock_list = self.data_loader.get_stock_list()

        # 获取每只股票的日线数据
        for code in stock_list:
            try:
                # 加载日线数据
                df = self.data_loader.load_stock_data_between_dates(
                    code, self.start_date, self.end_date
                )

                if not df.empty:
                    # 计算技术指标
                    self._calculate_indicators(df)
                    self.all_stocks_data[code] = df

            except Exception as e:
                print(f"加载股票{code}数据时出错: {str(e)}")
                continue

        print(f"成功加载 {len(self.all_stocks_data)} 只股票的数据")

    def _calculate_indicators(self, df):
        """计算技术指标"""
        # 计算移动平均
        df["ma5"] = df["close"].rolling(5).mean()
        df["ma10"] = df["close"].rolling(10).mean()
        df["ma20"] = df["close"].rolling(20).mean()

        # 计算动量指标
        df["momentum"] = df["close"].pct_change(5)

        # 计算波动率
        df["volatility"] = df["close"].rolling(20).std()

        # 计算RSI
        delta = df["close"].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df["rsi"] = 100 - (100 / (1 + rs))

        # 计算MACD
        exp1 = df["close"].ewm(span=12, adjust=False).mean()
        exp2 = df["close"].ewm(span=26, adjust=False).mean()
        df["macd"] = exp1 - exp2
        df["signal"] = df["macd"].ewm(span=9, adjust=False).mean()
        df["macd_hist"] = df["macd"] - df["signal"]

    def select_stocks(self, date):
        """选股"""
        # 创建当日所有股票的评分表
        scores = []

        for code, data in self.all_stocks_data.items():
            if date not in data.index:
                continue

            try:
                # ��取当前数据
                current = data.loc[date]

                # 计算评分指标
                momentum_score = current["momentum"] * 0.3  # 动量因子
                volatility_score = -current["volatility"] * 0.2  # 波动率因子（负相关）
                rsi_score = (current["rsi"] - 50) / 50 * 0.2  # RSI因子
                macd_score = current["macd_hist"] * 0.3  # MACD柱状图因子

                # 计算总分
                total_score = momentum_score + volatility_score + rsi_score + macd_score

                scores.append(
                    {
                        "code": code,
                        "score": total_score,
                        "close": current["close"],
                        "volume": current["volume"],
                    }
                )

            except Exception as e:
                print(f"计算股票{code}评分时出错: {str(e)}")
                continue

        # 按评分排序并选出前N只股票
        scores = sorted(scores, key=lambda x: x["score"], reverse=True)
        selected = scores[: self.stock_pool_size]

        # 更新每日选股池
        self.daily_stock_pool[date] = {
            item["code"]: {
                "score": item["score"],
                "close": item["close"],
                "volume": item["volume"],
            }
            for item in selected
        }

    def execute_trades(self, date):
        """执行交易"""
        # 先检查是否需要平仓
        self._check_stop_conditions(date)

        # 获取当日选股池
        if date not in self.daily_stock_pool:
            return

        daily_pool = self.daily_stock_pool[date]

        # 计算可用资金
        available_cash = self.cash

        # 计算每只股票的目标持仓金额
        target_position_value = available_cash / (
            self.max_positions - len(self.positions)
        )

        # 对选股池中的股票进行开仓
        for code, info in daily_pool.items():
            # 如果已经达到最大持仓数量，跳出
            if len(self.positions) >= self.max_positions:
                break

            # 如果已经持有该股票，跳过
            if code in self.positions:
                continue

            # 计算可买入数量（向下取整到手）
            price = info["close"]
            shares = int((target_position_value / price) / 100) * 100

            if shares > 0:
                cost = shares * price
                if cost <= available_cash:
                    # 记录交易
                    self.positions[code] = {
                        "shares": shares,
                        "cost": price,
                        "value": cost,
                        "entry_date": date,
                    }

                    # 更新现金
                    self.cash -= cost
                    available_cash -= cost

                    # 记录交易
                    self.trades.append(
                        {
                            "date": date,
                            "code": code,
                            "type": "买入",
                            "price": price,
                            "shares": shares,
                            "value": cost,
                            "cash": self.cash,
                        }
                    )

    def _check_stop_conditions(self, date):
        """检查止损止盈条件"""
        # 复制持仓字典，因为我们会在迭代过程中修改它
        positions = dict(self.positions)

        for code, position in positions.items():
            if code not in self.all_stocks_data:
                continue

            # 获取当前价格
            current_price = self.all_stocks_data[code].loc[date, "close"]

            # 计算收益率
            returns = (current_price - position["cost"]) / position["cost"]

            # 检查止损条件
            if returns <= self.stop_loss:
                self._close_position(code, current_price, date, "止损")
                continue

            # 检查止盈条件
            if returns >= self.stop_profit:
                self._close_position(code, current_price, date, "止盈")
                continue

            # 检查持仓时间（第二收盘前必须平仓）
            entry_date = position["entry_date"]
            if (date - entry_date).days >= 1:
                self._close_position(code, current_price, date, "到期平仓")

    def _close_position(self, code, price, date, reason):
        """平仓"""
        position = self.positions[code]
        shares = position["shares"]
        cost = position["cost"]

        # 计算收益
        value = shares * price
        profit = value - (shares * cost)

        # 更新现金
        self.cash += value

        # 记录交易
        self.trades.append(
            {
                "date": date,
                "code": code,
                "type": f"卖出({reason})",
                "price": price,
                "shares": shares,
                "value": value,
                "profit": profit,
                "cash": self.cash,
            }
        )

        # 删除持仓
        del self.positions[code]

    def run(self):
        """运行策略"""
        # 加载数据
        print("正在加载数据...")
        self.load_data()

        # 创建日期范围
        dates = pd.date_range(self.start_date, self.end_date, freq="B")

        # 记录每日净值
        nav_records = []

        # 按日期遍历
        print("开始回测...")
        for date in dates:
            # 跳过没有数据的日期
            if not any(date in data.index for data in self.all_stocks_data.values()):
                continue

            # 选股
            self.select_stocks(date)

            # 执行交易
            self.execute_trades(date)

            # 计算当日持仓市值
            portfolio_value = self.cash
            for code, position in self.positions.items():
                if date in self.all_stocks_data[code].index:
                    current_price = self.all_stocks_data[code].loc[date, "close"]
                    portfolio_value += position["shares"] * current_price

            # 记录净值
            nav_records.append(
                {"date": date, "nav": portfolio_value / self.initial_capital}
            )

            # 记录资金流水
            self.cash_flows.append(
                {
                    "date": date,
                    "type": "每日结算",
                    "amount": portfolio_value - self.initial_capital,
                    "balance": portfolio_value,
                }
            )

        # 转换成DataFrame
        self.nav_series = pd.DataFrame(nav_records).set_index("date")["nav"]
        self.trades = pd.DataFrame(self.trades)
        self.cash_flows = pd.DataFrame(self.cash_flows)

        # 计算策略指标
        metrics = self._calculate_metrics()

        print("回测完成!")
        return {
            "metrics": metrics,
            "trades": self.trades,
            "cash_flows": self.cash_flows,
            "nav_series": self.nav_series,
            "positions": self.positions,
        }

    def _calculate_metrics(self):
        """计算策略指标"""
        # 计算收益率序列
        returns = self.nav_series.pct_change().dropna()

        # 计算年化收益率
        total_return = (self.nav_series.iloc[-1] / self.nav_series.iloc[0]) - 1
        years = (self.end_date - self.start_date).days / 365
        annual_return = (1 + total_return) ** (1 / years) - 1

        # 计算波动率
        volatility = returns.std() * np.sqrt(252)

        # 计算最大回撤
        nav_max = self.nav_series.expanding().max()
        drawdown = (self.nav_series - nav_max) / nav_max
        max_drawdown = drawdown.min()
        current_drawdown = drawdown.iloc[-1]

        # 计算夏普比率
        risk_free_rate = 0.03  # 假设无风险利率为3%
        excess_returns = returns - risk_free_rate / 252
        sharpe_ratio = np.sqrt(252) * excess_returns.mean() / returns.std()

        # 计算卡玛比率
        calmar_ratio = annual_return / abs(max_drawdown) if abs(max_drawdown) > 0 else 0

        # 计算胜率
        profitable_trades = len(self.trades[self.trades["profit"] > 0])
        total_trades = len(self.trades[self.trades["type"].str.startswith("卖出")])
        win_rate = profitable_trades / total_trades if total_trades > 0 else 0

        # 计算盈亏比
        avg_profit = (
            self.trades[self.trades["profit"] > 0]["profit"].mean()
            if profitable_trades > 0
            else 0
        )
        avg_loss = (
            abs(self.trades[self.trades["profit"] < 0]["profit"].mean())
            if len(self.trades[self.trades["profit"] < 0]) > 0
            else 1
        )
        profit_ratio = avg_profit / avg_loss if avg_loss != 0 else 0

        return {
            "nav": self.nav_series.iloc[-1],
            "annual_return": annual_return * 100,
            "volatility": volatility * 100,
            "max_drawdown": max_drawdown * 100,
            "current_drawdown": current_drawdown * 100,
            "sharpe_ratio": sharpe_ratio,
            "calmar_ratio": calmar_ratio,
            "win_rate": win_rate * 100,
            "profit_ratio": profit_ratio * 100,
            "total_trades": total_trades,
        }
