import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from .data_loader import LocalDataLoader


class MomentumStrategy:
    """动量策略"""

    def __init__(
        self,
        start_date,
        end_date,
        lookback_period=20,
        holding_period=10,
        data_dir=None,  # 添加数据目录参数
        initial_capital=1000000,  # 添加初始资金参数
    ):
        """初始化策略

        参数:
            start_date: 回测开始日期
            end_date: 回测结束日期
            lookback_period: 回看期(天)
            holding_period: 持有期(天)
            data_dir: 数据目录路径
            initial_capital: 初始资金
        """
        self.start_date = start_date
        self.end_date = end_date
        self.lookback_period = lookback_period
        self.holding_period = holding_period
        self.data_dir = data_dir
        self.initial_capital = initial_capital

        # 初始化数据加载器
        if data_dir:
            self.data_loader = LocalDataLoader(data_dir)

        # 初始化数据
        self.data = None
        self.trades = []
        self.cash_flows = []
        self.nav_series = None
        self.position = 0
        self.cash = initial_capital

    def load_data(self):
        """加载数据"""
        if not self.data_dir:
            raise ValueError("未指定数据目录")

        print("正在加载本地数据...")
        try:
            # 使用上证指数作为交易标的
            df = self.data_loader.load_stock_data_between_dates(
                "sh.000001", self.start_date, self.end_date
            )

            if df.empty:
                raise ValueError("未找到数据")

            self.data = df
            print("数据加载完成")

        except Exception as e:
            print(f"加载数据时出错: {str(e)}")
            raise e

    def calculate_signals(self):
        """计算交易信号"""
        # 计算动量因子
        self.data["momentum"] = self.data["close"].pct_change(self.lookback_period)

        # 生成交易信号
        self.data["signal"] = 0
        self.data.loc[self.data["momentum"] > 0, "signal"] = 1  # 动量为正做多
        self.data.loc[self.data["momentum"] < 0, "signal"] = -1  # 动量为负做空

    def run(self):
        """运行策略"""
        # 加载数据
        self.load_data()

        # 计算信号
        self.calculate_signals()

        # 模拟交易
        nav = []
        position = 0
        cash = self.cash
        entry_price = None

        for i in range(len(self.data)):
            date = self.data.index[i]
            price = self.data["close"].iloc[i]
            signal = self.data["signal"].iloc[i]

            # 更新持仓
            if signal != position:
                # 平仓
                if position != 0:
                    profit = position * (price - entry_price) * size
                    cash += profit
                    self.trades.append(
                        {
                            "date": date,
                            "type": "平仓",
                            "price": price,
                            "size": size,
                            "profit": profit,
                        }
                    )

                # 开仓
                if signal != 0:
                    position = signal
                    entry_price = price
                    size = cash * 0.95 / price  # 使用95%资金开仓
                    self.trades.append(
                        {
                            "date": date,
                            "type": "开仓",
                            "price": price,
                            "size": size,
                            "profit": 0,
                        }
                    )

            # 计算当日净值
            if position != 0:
                nav_value = cash + position * (price - entry_price) * size
            else:
                nav_value = cash

            nav.append(nav_value)

            # 记录资金流水
            self.cash_flows.append(
                {
                    "date": date,
                    "type": "每日结算",
                    "amount": nav_value - cash,
                    "balance": nav_value,
                }
            )

            cash = nav_value

        # 转换成DataFrame
        self.nav_series = pd.Series(nav, index=self.data.index)
        self.trades = pd.DataFrame(self.trades)
        self.cash_flows = pd.DataFrame(self.cash_flows)

        # 计算策略指标
        metrics = self.calculate_metrics()

        return {
            "metrics": metrics,
            "trades": self.trades,
            "cash_flows": self.cash_flows,
            "nav_series": self.nav_series,
            "kline_data": self.data,
        }

    def calculate_metrics(self):
        """计算策略指标"""
        if self.nav_series is None or len(self.nav_series) == 0:
            return {
                "nav": 1.0,
                "annual_return": 0.0,
                "volatility": 0.0,
                "max_drawdown": 0.0,
                "current_drawdown": 0.0,
                "sharpe_ratio": 0.0,
                "calmar_ratio": 0.0,
                "win_rate": 0.0,
                "profit_ratio": 0.0,
                "total_trades": 0,
            }

        # 计算收益率序列
        returns = self.nav_series.pct_change().dropna()

        # 计算年化收益率
        total_return = (self.nav_series.iloc[-1] / self.nav_series.iloc[0]) - 1
        years = (self.end_date - self.start_date).days / 365
        annual_return = (1 + total_return) ** (1 / years) - 1

        # 计算波动率
        volatility = returns.std() * np.sqrt(252)

        # 计算最大回撤
        nav_max = self.nav_series.expanding().max()
        drawdown = (self.nav_series - nav_max) / nav_max
        max_drawdown = drawdown.min()
        current_drawdown = drawdown.iloc[-1]

        # 计算夏普比率
        risk_free_rate = 0.03  # 假设无风险利率为3%
        excess_returns = returns - risk_free_rate / 252
        sharpe_ratio = np.sqrt(252) * excess_returns.mean() / returns.std()

        # 计算卡玛比率
        calmar_ratio = annual_return / abs(max_drawdown) if abs(max_drawdown) > 0 else 0

        # 计算胜率
        profitable_trades = len(self.trades[self.trades["profit"] > 0])
        total_trades = len(self.trades)
        win_rate = profitable_trades / total_trades if total_trades > 0 else 0

        # 计算盈亏比
        avg_profit = (
            self.trades[self.trades["profit"] > 0]["profit"].mean()
            if profitable_trades > 0
            else 0
        )
        avg_loss = (
            abs(self.trades[self.trades["profit"] < 0]["profit"].mean())
            if len(self.trades[self.trades["profit"] < 0]) > 0
            else 1
        )
        profit_ratio = avg_profit / avg_loss if avg_loss != 0 else 0

        return {
            "nav": self.nav_series.iloc[-1],
            "annual_return": annual_return * 100,
            "volatility": volatility * 100,
            "max_drawdown": max_drawdown * 100,
            "current_drawdown": current_drawdown * 100,
            "sharpe_ratio": sharpe_ratio,
            "calmar_ratio": calmar_ratio,
            "win_rate": win_rate * 100,
            "profit_ratio": profit_ratio * 100,
            "total_trades": total_trades,
        }
