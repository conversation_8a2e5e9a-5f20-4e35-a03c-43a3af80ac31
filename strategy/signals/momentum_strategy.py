import pandas as pd
import numpy as np
from typing import List, Dict
from datetime import datetime, timedelta
import os
from strategy.base.strategy_base import StrategyBase


class MomentumStrategy(StrategyBase):
    """动量策略"""

    def __init__(
        self,
        data_dir: str,
        start_date: str = "2022-01-01",
        lookback_days: int = 60,
        momentum_window: int = 20,
        top_n: int = 5,
    ):
        """
        初始化动量策略

        Parameters:
        -----------
        data_dir : str
            数据目录路径
        start_date : str
            回测起始日期
        lookback_days : int
            历史数据回看天数
        momentum_window : int
            动量计算窗口
        top_n : int
            选股数量
        """
        super().__init__(data_dir, start_date)
        self.lookback_days = lookback_days
        self.momentum_window = momentum_window
        self.top_n = top_n

    def load_history_data(
        self, codes: List[str], lookback_days: int = None
    ) -> pd.DataFrame:
        """载载历史数据"""
        if lookback_days is None:
            lookback_days = self.lookback_days

        # 计算起始日期
        start_date = self.current_date - timedelta(days=lookback_days)

        # 用于存储所有股票的数据
        all_data = []

        for code in codes:
            # 构建文件路径
            file_path = os.path.join(self.data_dir, "stocks", code, "day.csv")

            if os.path.exists(file_path):
                # 读取数据
                df = pd.read_csv(file_path)
                df["date"] = pd.to_datetime(df["date"])
                df = df.set_index("date")

                # 过滤日期范围
                df = df[(df.index >= start_date) & (df.index <= self.current_date)]

                # 添加股票代码列
                df["code"] = code
                all_data.append(df)

        if not all_data:
            return pd.DataFrame()

        # 合并所有数据
        combined_data = pd.concat(all_data)

        # 转换为多层索引
        combined_data = combined_data.pivot(
            columns="code", values=["open", "high", "low", "close", "volume"]
        )

        return combined_data

    def generate_signals(self, date: datetime) -> List[str]:
        """生成交易信号"""
        self.current_date = date

        # 获取股票池
        stock_pool = self._get_stock_pool()

        if not stock_pool:
            return []

        # 加载历史数据
        history_data = self.load_history_data(stock_pool)

        if history_data.empty:
            return []

        # 计算动量因子
        momentum_scores = self._calculate_momentum(history_data)

        # 选择前N只股票
        selected_stocks = momentum_scores.nlargest(self.top_n).index.tolist()

        return selected_stocks

    def should_sell(self, code: str, holding_days: int) -> bool:
        """判断是否应该卖出"""
        # 持仓超过2天就卖出
        return holding_days >= 2

    def _get_stock_pool(self) -> List[str]:
        """获取股票池"""
        # 这里简单返回几只示例股票，实际应该根据需求实现
        return [
            "sh.600000",
            "sh.600036",
            "sh.601318",
            "sh.600519",
            "sh.600276",
            "sh.601166",
            "sh.600030",
            "sh.600887",
            "sh.601668",
            "sh.600016",
        ]

    def _calculate_momentum(self, data: pd.DataFrame) -> pd.Series:
        """计算动量因子"""
        # ��取收盘价数据
        close_prices = data["close"]

        # 计算动量（过去N天的收益率）
        momentum = close_prices.iloc[-1] / close_prices.iloc[-self.momentum_window] - 1

        return momentum
