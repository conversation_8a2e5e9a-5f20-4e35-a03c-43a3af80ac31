#!/bin/bash

file_to_run="data_loader/download_data.py"

while true
do
    # 获取当前时间
    current_hour=$(date +%H)
    current_minute=$(date +%M)
    
    # 判断是否是早上7点
    if [ "$current_hour" = "07" ] && [ "$current_minute" = "00" ]; then
        echo "$(date): start download data..."
        python "$file_to_run"
        # 执行完后睡眠59分钟，避免在这一个小时内重复执行
        sleep 3540  # 59分钟 = 3540秒
    else
        # 每分钟检查一次
        sleep 60
    fi
done