#!/usr/bin/env python3
"""
快速数据更新脚本
提供多种预设模式，方便快速启动数据更新
"""
import argparse
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock.stock_data import StockData
from update_config import DataUpdateConfig, PresetConfigs
from update_data import update_basic_data, update_index_data_parallel, update_stock_data_parallel
from performance_monitor import log_system_info


def setup_logging(log_level: str = "INFO", log_file: str = None):
    """设置日志"""
    handlers = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=handlers
    )


def main():
    parser = argparse.ArgumentParser(description='A股数据快速更新工具')
    parser.add_argument('--mode', choices=['fast', 'full', 'conservative', 'custom'], 
                       default='fast', help='更新模式')
    parser.add_argument('--data-dir', type=str, 
                       default='/home/<USER>/Studios/700-dataset/stocks',
                       help='数据存储目录')
    parser.add_argument('--workers', type=int, default=None,
                       help='并发进程数')
    parser.add_argument('--daily', action='store_true', default=True,
                       help='更新日线数据')
    parser.add_argument('--5min', action='store_true', 
                       help='更新5分钟数据')
    parser.add_argument('--15min', action='store_true',
                       help='更新15分钟数据')
    parser.add_argument('--30min', action='store_true',
                       help='更新30分钟数据')
    parser.add_argument('--60min', action='store_true',
                       help='更新60分钟数据')
    parser.add_argument('--stocks-only', action='store_true',
                       help='仅更新股票数据，跳过基础数据和指数')
    parser.add_argument('--basic-only', action='store_true',
                       help='仅更新基础数据')
    parser.add_argument('--stock-list', type=str,
                       help='指定股票列表文件（每行一个股票代码）')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    parser.add_argument('--log-file', type=str, default='quick_update.log',
                       help='日志文件')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level, args.log_file)
    
    # 设置数据目录
    StockData().setup(data_dir=args.data_dir)
    logging.info(f"数据目录: {args.data_dir}")
    
    # 创建配置
    if args.mode == 'fast':
        config = PresetConfigs.fast_daily_only()
        logging.info("使用快速模式配置")
    elif args.mode == 'full':
        config = PresetConfigs.full_data()
        logging.info("使用完整模式配置")
    elif args.mode == 'conservative':
        config = PresetConfigs.conservative()
        logging.info("使用保守模式配置")
    else:  # custom
        config = DataUpdateConfig()
        config.enable_daily = args.daily
        config.enable_5min = getattr(args, '5min', False)
        config.enable_15min = getattr(args, '15min', False)
        config.enable_30min = getattr(args, '30min', False)
        config.enable_60min = getattr(args, '60min', False)
        logging.info("使用自定义配置")
    
    # 覆盖进程数设置
    if args.workers:
        config.max_workers = args.workers
    
    # 处理股票列表
    if args.stock_list:
        try:
            with open(args.stock_list, 'r') as f:
                stock_codes = [line.strip() for line in f if line.strip()]
            config.stock_pools = stock_codes
            logging.info(f"从文件加载股票列表: {len(stock_codes)} 只股票")
        except FileNotFoundError:
            logging.error(f"股票列表文件不存在: {args.stock_list}")
            return 1
    
    # 验证配置
    try:
        config.validate()
    except ValueError as e:
        logging.error(f"配置验证失败: {e}")
        return 1
    
    # 显示配置信息
    logging.info("更新配置:")
    logging.info(f"  启用数据类型: {config.get_enabled_data_types()}")
    logging.info(f"  最大进程数: {config.max_workers}")
    logging.info(f"  重试次数: {config.retry_times}")
    if config.stock_pools:
        logging.info(f"  股票池大小: {len(config.stock_pools)}")
    
    # 记录系统信息
    log_system_info()
    
    try:
        if args.basic_only:
            # 仅更新基础数据
            logging.info("仅更新基础数据模式")
            update_basic_data()
        elif args.stocks_only:
            # 仅更新股票数据
            logging.info("仅更新股票数据模式")
            update_stock_data_parallel(config)
        else:
            # 完整更新流程
            logging.info("开始完整数据更新流程")
            
            # 1. 更新基础数据
            update_basic_data()
            
            # 2. 并行更新指数数据
            update_index_data_parallel(config)
            
            # 3. 并行更新股票数据
            update_stock_data_parallel(config)
        
        logging.info("数据更新完成！")
        return 0
        
    except KeyboardInterrupt:
        logging.warning("用户中断了更新过程")
        return 1
    except Exception as e:
        logging.error(f"数据更新过程中发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
