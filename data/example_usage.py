#!/usr/bin/env python3
"""
数据下载使用示例
演示如何使用优化后的数据下载功能
"""
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock.stock_data import StockData
from update_config import DataUpdateConfig, PresetConfigs
from update_data import update_basic_data, update_stock_data_parallel
from batch_downloader import BatchDownloader
from performance_monitor import PerformanceMonitor, log_system_info


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('example_usage.log'),
            logging.StreamHandler()
        ]
    )


def example_1_quick_start():
    """示例1：快速开始"""
    print("=" * 50)
    print("示例1：快速开始 - 使用预设配置")
    print("=" * 50)
    
    # 设置数据目录
    data_dir = "/tmp/stock_data_example"
    StockData().setup(data_dir=data_dir)
    
    # 使用快速模式配置
    config = PresetConfigs.fast_daily_only()
    config.max_workers = 2  # 减少并发数用于演示
    
    logging.info("使用快速模式配置")
    logging.info(f"数据目录: {data_dir}")
    logging.info(f"启用数据类型: {config.get_enabled_data_types()}")
    
    # 更新基础数据
    try:
        update_basic_data()
        logging.info("基础数据更新完成")
    except Exception as e:
        logging.error(f"基础数据更新失败: {e}")


def example_2_custom_config():
    """示例2：自定义配置"""
    print("=" * 50)
    print("示例2：自定义配置")
    print("=" * 50)
    
    # 创建自定义配置
    config = DataUpdateConfig()
    config.enable_daily = True
    config.enable_5min = False
    config.enable_15min = True  # 启用15分钟数据
    config.enable_30min = False
    config.enable_60min = False
    config.max_workers = 3
    config.retry_times = 2
    
    logging.info("自定义配置:")
    logging.info(f"  启用数据类型: {config.get_enabled_data_types()}")
    logging.info(f"  最大进程数: {config.max_workers}")
    logging.info(f"  重试次数: {config.retry_times}")
    
    # 验证配置
    try:
        config.validate()
        logging.info("配置验证通过")
    except ValueError as e:
        logging.error(f"配置验证失败: {e}")


def example_3_specific_stocks():
    """示例3：更新指定股票"""
    print("=" * 50)
    print("示例3：更新指定股票")
    print("=" * 50)
    
    # 指定要更新的股票
    stock_codes = ['sh.600000', 'sh.600036', 'sz.000001', 'sz.000002']
    
    config = DataUpdateConfig()
    config.enable_daily = True
    config.max_workers = 2
    config.stock_pools = stock_codes
    
    logging.info(f"指定更新股票: {stock_codes}")
    
    # 使用批量下载器
    downloader = BatchDownloader(config)
    tasks = downloader.create_stock_tasks(stock_codes, StockData().data_dir)
    
    logging.info(f"创建了 {len(tasks)} 个下载任务")
    
    # 模拟执行（实际使用时取消注释）
    # result = downloader.download_parallel(tasks)
    # logging.info(f"下载结果: 成功 {result['success_count']}, 失败 {result['failed_count']}")


def example_4_performance_monitoring():
    """示例4：性能监控"""
    print("=" * 50)
    print("示例4：性能监控")
    print("=" * 50)
    
    # 创建性能监控器
    monitor = PerformanceMonitor("示例任务")
    
    # 模拟任务
    total_tasks = 100
    monitor.start_monitoring(total_tasks)
    
    import time
    for i in range(total_tasks):
        # 模拟工作
        time.sleep(0.01)
        
        # 更新进度（90%成功率）
        success = (i % 10 != 0)
        monitor.update_progress(success=success)
        
        # 每20个任务记录一次进度
        if (i + 1) % 20 == 0:
            monitor.log_progress(interval=20)
    
    monitor.finish_monitoring()
    
    # 显示最终状态
    final_status = monitor.get_current_status()
    logging.info("最终统计:")
    logging.info(f"  完成率: {final_status['progress_percent']:.1f}%")
    logging.info(f"  成功率: {final_status['success_rate']:.1f}%")
    logging.info(f"  总耗时: {final_status['elapsed_time_minutes']:.2f}分钟")


def example_5_system_info():
    """示例5：系统信息检查"""
    print("=" * 50)
    print("示例5：系统信息检查")
    print("=" * 50)
    
    # 记录系统信息
    log_system_info()
    
    # 检查系统资源
    from performance_monitor import check_system_resources
    resources = check_system_resources()
    
    print("\n系统资源详情:")
    print(f"CPU核心数: {resources['cpu_count']}")
    print(f"CPU使用率: {resources['cpu_percent']:.1f}%")
    print(f"内存总量: {resources['memory_total_gb']:.1f} GB")
    print(f"内存可用: {resources['memory_available_gb']:.1f} GB")
    print(f"内存使用率: {resources['memory_percent']:.1f}%")
    print(f"磁盘总量: {resources['disk_total_gb']:.1f} GB")
    print(f"磁盘可用: {resources['disk_free_gb']:.1f} GB")
    print(f"磁盘使用率: {resources['disk_percent']:.1f}%")


def main():
    """主函数"""
    setup_logging()
    
    print("A股数据下载优化版本 - 使用示例")
    print("=" * 60)
    
    try:
        # 运行所有示例
        example_1_quick_start()
        example_2_custom_config()
        example_3_specific_stocks()
        example_4_performance_monitoring()
        example_5_system_info()
        
        print("\n" + "=" * 60)
        print("所有示例运行完成！")
        print("详细日志请查看 example_usage.log")
        
    except Exception as e:
        logging.error(f"示例运行出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
