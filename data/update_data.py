import baostock as bs
import logging
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import Dict
import os

from stock.stock_data import StockData
from stock.stock_daily import StockDaily
from stock.stock_5min import Stock5Min
from stock.stock_15min import Stock15Min
from stock.stock_30min import Stock30Min
from stock.stock_60min import Stock60Min
from index.index_daily import IndexDaily
from update_config import DataUpdateConfig, PresetConfigs


def update_for_stock(stock_code: str, data_dir: str, config: DataUpdateConfig) -> Dict:
    """
    更新单只股票的各种数据
    Args:
        stock_code: 股票代码
        data_dir: 数据目录
        config: 更新配置
    Returns:
        Dict: 更新结果
    """
    result = {
        "stock_code": stock_code,
        "success": True,
        "error": None,
        "updated_types": [],
    }

    try:
        # 在子进程中需要重新登录baostock
        bs.login()

        if config.enable_daily:
            StockDaily(data_dir=data_dir, stock_code=stock_code).update()
            result["updated_types"].append("daily")

        if config.enable_5min:
            Stock5Min(data_dir=data_dir, stock_code=stock_code).update()
            result["updated_types"].append("5min")

        if config.enable_15min:
            Stock15Min(data_dir=data_dir, stock_code=stock_code).update()
            result["updated_types"].append("15min")

        if config.enable_30min:
            Stock30Min(data_dir=data_dir, stock_code=stock_code).update()
            result["updated_types"].append("30min")

        if config.enable_60min:
            Stock60Min(data_dir=data_dir, stock_code=stock_code).update()
            result["updated_types"].append("60min")

    except Exception as e:
        result["success"] = False
        result["error"] = str(e)
        logging.error(f"更新股票 {stock_code} 数据失败: {e}")
    finally:
        bs.logout()

    return result


def update_index_data(index_code: str, data_dir: str) -> Dict:
    """
    更新单个指数数据
    Args:
        index_code: 指数代码
        data_dir: 数据目录
    Returns:
        Dict: 更新结果
    """
    result = {"index_code": index_code, "success": True, "error": None}

    try:
        bs.login()
        IndexDaily(data_dir=data_dir, index_code=index_code).update()
    except Exception as e:
        result["success"] = False
        result["error"] = str(e)
        logging.error(f"更新指数 {index_code} 数据失败: {e}")
    finally:
        bs.logout()

    return result


def update_basic_data():
    """更新基础数据（股票列表、指数列表等）"""
    logging.info("开始更新基础数据...")
    bs.login()

    try:
        # 获取证券市场基本数据，每周更新一次
        StockData().stock_basic.update()  # 股票基本信息
        StockData().index_basic.update()  # 指数基本信息
        StockData().cb_basic.update()  # 可转债基本信息
        StockData().etf_basic.update()  # ETF基本信息
        StockData().stock_company.update()  # 上市公司信息
        StockData().hs300.update()  # 沪深300成分股
        StockData().zz500.update()  # 中证500成分股
        logging.info("基础数据更新完成")
    except Exception as e:
        logging.error(f"基础数据更新失败: {e}")
        raise
    finally:
        bs.logout()


def update_index_data_parallel(config: DataUpdateConfig):
    """并行更新指数数据 - 优化版本"""
    from batch_downloader import download_index_data_optimized

    index_list = StockData().index_basic.index_codes()
    if not index_list:
        logging.warning("没有找到指数列表")
        return

    logging.info(f"开始更新 {len(index_list)} 个指数的数据...")

    result = download_index_data_optimized(index_list, StockData().data_dir, config)

    logging.info(
        f"指数数据更新完成: 成功 {result['success_count']}, 失败 {result['failed_count']}"
    )

    if result["failed_tasks"]:
        logging.warning(f"失败的指数: {[task.code for task in result['failed_tasks']]}")


def update_stock_data_parallel(config: DataUpdateConfig):
    """并行更新股票数据 - 优化版本"""
    from batch_downloader import download_stock_data_optimized

    stock_list = StockData().stock_basic.stock_codes()
    if not stock_list:
        logging.warning("没有找到股票列表")
        return

    # 可选：过滤股票列表
    if config.stock_pools:
        # 如果指定了股票池，只更新指定的股票
        stock_list = [code for code in stock_list if code in config.stock_pools]
        logging.info(f"使用指定股票池，筛选后股票数量: {len(stock_list)}")

    logging.info(f"开始更新 {len(stock_list)} 只股票的数据...")
    logging.info(f"启用的数据类型: {config.get_enabled_data_types()}")
    logging.info(f"使用 {config.max_workers} 个进程并行处理")

    result = download_stock_data_optimized(stock_list, StockData().data_dir, config)

    logging.info(
        f"股票数据更新完成: 成功 {result['success_count']}, 失败 {result['failed_count']}"
    )

    if result["failed_tasks"]:
        failed_codes = [task.code for task in result["failed_tasks"]]
        logging.warning(f"失败的股票数量: {len(failed_codes)}")
        logging.warning(f"部分失败股票: {failed_codes[:10]}...")  # 只显示前10个

        # 保存失败列表到文件
        with open("failed_stocks.txt", "w") as f:
            for code in failed_codes:
                f.write(f"{code}\n")
        logging.info("失败股票列表已保存到 failed_stocks.txt")


if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[logging.FileHandler("data_update.log"), logging.StreamHandler()],
    )

    # 设置数据存储目录
    # StockData().setup(data_dir="/home/<USER>/Studios/600-codes/dataset/trade_data")
    StockData().setup(data_dir="/home/<USER>/Studios/700-dataset/stocks")
    # StockData().setup(data_dir="E:\600-codes\009-dataset")

    # 创建配置 - 可以选择预定义配置或自定义
    # config = PresetConfigs.fast_daily_only()  # 快速模式：仅日线数据
    # config = PresetConfigs.full_data()        # 完整模式：所有数据
    # config = PresetConfigs.conservative()     # 保守模式：低并发

    # 自定义配置
    config = DataUpdateConfig()
    config.enable_daily = True
    config.enable_5min = False  # 5分钟数据量大，可选择性开启
    config.enable_15min = False
    config.enable_30min = False
    config.enable_60min = False
    config.max_workers = 6  # 根据网络和系统性能调整

    # 可选：指定股票池（用于测试或特定需求）
    # config.stock_pools = ['sh.600000', 'sh.600036', 'sz.000001']  # 仅更新指定股票

    # 验证配置
    config.validate()

    logging.info("数据更新配置:")
    logging.info(f"  启用数据类型: {config.get_enabled_data_types()}")
    logging.info(f"  最大进程数: {config.max_workers}")
    logging.info(f"  重试次数: {config.retry_times}")

    # 记录系统信息
    from performance_monitor import log_system_info

    log_system_info()

    try:
        # 1. 更新基础数据
        update_basic_data()

        # 2. 并行更新指数数据
        update_index_data_parallel(config)

        # 3. 并行更新股票数据
        update_stock_data_parallel(config)

        logging.info("所有数据更新完成！")

    except Exception as e:
        logging.error(f"数据更新过程中发生错误: {e}")
        raise
