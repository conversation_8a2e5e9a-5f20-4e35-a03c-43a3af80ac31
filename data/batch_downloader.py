"""
批量数据下载器 - 优化版本
"""
import baostock as bs
import logging
import time
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import List, Dict, Callable, Any
from dataclasses import dataclass
import queue
import threading

from update_config import DataUpdateConfig
from performance_monitor import PerformanceMonitor, ProgressBar, log_system_info


@dataclass
class DownloadTask:
    """下载任务"""
    task_id: str
    task_type: str  # 'stock', 'index', 'basic'
    code: str
    data_dir: str
    config: DataUpdateConfig
    retry_count: int = 0


class BatchDownloader:
    """批量下载器"""
    
    def __init__(self, config: DataUpdateConfig):
        self.config = config
        self.monitor = PerformanceMonitor("BatchDownloader")
        self.failed_tasks: List[DownloadTask] = []
        self.success_count = 0
        self.failed_count = 0
        
    def create_stock_tasks(self, stock_codes: List[str], data_dir: str) -> List[DownloadTask]:
        """创建股票下载任务"""
        tasks = []
        for code in stock_codes:
            task = DownloadTask(
                task_id=f"stock_{code}",
                task_type="stock",
                code=code,
                data_dir=data_dir,
                config=self.config
            )
            tasks.append(task)
        return tasks
    
    def create_index_tasks(self, index_codes: List[str], data_dir: str) -> List[DownloadTask]:
        """创建指数下载任务"""
        tasks = []
        for code in index_codes:
            task = DownloadTask(
                task_id=f"index_{code}",
                task_type="index",
                code=code,
                data_dir=data_dir,
                config=self.config
            )
            tasks.append(task)
        return tasks
    
    def execute_task(self, task: DownloadTask) -> Dict:
        """执行单个下载任务"""
        result = {
            'task_id': task.task_id,
            'task_type': task.task_type,
            'code': task.code,
            'success': False,
            'error': None,
            'retry_count': task.retry_count,
            'updated_types': []
        }
        
        try:
            # 在子进程中需要重新登录baostock
            bs.login()
            
            if task.task_type == "stock":
                result.update(self._download_stock_data(task))
            elif task.task_type == "index":
                result.update(self._download_index_data(task))
            else:
                raise ValueError(f"未知任务类型: {task.task_type}")
                
            result['success'] = True
            
        except Exception as e:
            result['error'] = str(e)
            logging.error(f"任务 {task.task_id} 执行失败: {e}")
        finally:
            bs.logout()
        
        return result
    
    def _download_stock_data(self, task: DownloadTask) -> Dict:
        """下载股票数据"""
        from stock.stock_daily import StockDaily
        from stock.stock_5min import Stock5Min
        from stock.stock_15min import Stock15Min
        from stock.stock_30min import Stock30Min
        from stock.stock_60min import Stock60Min
        
        updated_types = []
        
        if task.config.enable_daily:
            StockDaily(data_dir=task.data_dir, stock_code=task.code).update()
            updated_types.append('daily')
            
        if task.config.enable_5min:
            Stock5Min(data_dir=task.data_dir, stock_code=task.code).update()
            updated_types.append('5min')
            
        if task.config.enable_15min:
            Stock15Min(data_dir=task.data_dir, stock_code=task.code).update()
            updated_types.append('15min')
            
        if task.config.enable_30min:
            Stock30Min(data_dir=task.data_dir, stock_code=task.code).update()
            updated_types.append('30min')
            
        if task.config.enable_60min:
            Stock60Min(data_dir=task.data_dir, stock_code=task.code).update()
            updated_types.append('60min')
        
        return {'updated_types': updated_types}
    
    def _download_index_data(self, task: DownloadTask) -> Dict:
        """下载指数数据"""
        from index.index_daily import IndexDaily
        
        IndexDaily(data_dir=task.data_dir, index_code=task.code).update()
        return {'updated_types': ['daily']}
    
    def download_parallel(self, tasks: List[DownloadTask]) -> Dict:
        """并行下载"""
        if not tasks:
            return {'success_count': 0, 'failed_count': 0, 'failed_tasks': []}
        
        logging.info(f"开始并行下载，任务数: {len(tasks)}, 进程数: {self.config.max_workers}")
        log_system_info()
        
        self.monitor.start_monitoring(len(tasks))
        progress_bar = ProgressBar(len(tasks))
        
        success_count = 0
        failed_count = 0
        failed_tasks = []
        
        with ProcessPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(self.execute_task, task): task
                for task in tasks
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    result = future.result()
                    
                    if result['success']:
                        success_count += 1
                        self.monitor.update_progress(success=True)
                        logging.debug(f"任务 {result['task_id']} 完成: {result['updated_types']}")
                    else:
                        failed_count += 1
                        self.monitor.update_progress(success=False)
                        
                        # 重试逻辑
                        if task.retry_count < self.config.retry_times:
                            task.retry_count += 1
                            logging.warning(f"任务 {task.task_id} 失败，准备重试 ({task.retry_count}/{self.config.retry_times})")
                            time.sleep(self.config.retry_delay)
                            # 重新提交任务
                            retry_future = executor.submit(self.execute_task, task)
                            future_to_task[retry_future] = task
                        else:
                            failed_tasks.append(task)
                            logging.error(f"任务 {task.task_id} 最终失败: {result['error']}")
                    
                    progress_bar.update()
                    self.monitor.log_progress(interval=50)
                    
                except Exception as e:
                    failed_count += 1
                    failed_tasks.append(task)
                    logging.error(f"任务 {task.task_id} 执行异常: {e}")
                    progress_bar.update()
                    self.monitor.update_progress(success=False)
        
        progress_bar.finish()
        self.monitor.finish_monitoring()
        
        return {
            'success_count': success_count,
            'failed_count': failed_count,
            'failed_tasks': failed_tasks
        }
    
    def download_with_retry(self, tasks: List[DownloadTask]) -> Dict:
        """带重试的下载"""
        all_results = []
        remaining_tasks = tasks.copy()
        
        for attempt in range(self.config.retry_times + 1):
            if not remaining_tasks:
                break
                
            if attempt > 0:
                logging.info(f"第 {attempt + 1} 次重试，剩余任务: {len(remaining_tasks)}")
                time.sleep(self.config.retry_delay)
            
            result = self.download_parallel(remaining_tasks)
            all_results.append(result)
            
            # 准备下次重试的任务
            remaining_tasks = result['failed_tasks']
        
        # 汇总结果
        total_success = sum(r['success_count'] for r in all_results)
        total_failed = len(remaining_tasks)
        
        logging.info(f"下载完成: 成功 {total_success}, 失败 {total_failed}")
        
        return {
            'success_count': total_success,
            'failed_count': total_failed,
            'failed_tasks': remaining_tasks,
            'all_results': all_results
        }


def download_stock_data_optimized(stock_codes: List[str], data_dir: str, config: DataUpdateConfig) -> Dict:
    """优化的股票数据下载函数"""
    downloader = BatchDownloader(config)
    tasks = downloader.create_stock_tasks(stock_codes, data_dir)
    return downloader.download_with_retry(tasks)


def download_index_data_optimized(index_codes: List[str], data_dir: str, config: DataUpdateConfig) -> Dict:
    """优化的指数数据下载函数"""
    downloader = BatchDownloader(config)
    tasks = downloader.create_index_tasks(index_codes, data_dir)
    return downloader.download_with_retry(tasks)


if __name__ == "__main__":
    # 测试批量下载器
    from update_config import PresetConfigs
    
    config = PresetConfigs.fast_daily_only()
    config.max_workers = 2
    
    # 模拟测试
    test_codes = ['sh.000001', 'sz.000001', 'sh.000002']
    downloader = BatchDownloader(config)
    tasks = downloader.create_stock_tasks(test_codes, "/tmp/test_data")
    
    print("测试批量下载器...")
    result = downloader.download_parallel(tasks)
    print(f"结果: {result}")
