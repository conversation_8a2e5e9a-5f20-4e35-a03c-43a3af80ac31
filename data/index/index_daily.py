import logging
import os
import baostock as bs
import pandas as pd
import colorama
import numpy as np

from datetime import date, timedelta
from typing import Union, List
from stock.stock_data import StockData
from tools.helper import need_update_by_trade_date


class IndexDaily(object):
    base_date = date(year=2006, month=1, day=1)

    def __init__(self, data_dir: str, index_code: str):
        self.data_dir = data_dir
        self.index_code = index_code
        self.dataframe: Union[pd.DataFrame, None] = None

    def file_path(self) -> str:
        """
        返回保存数据的csv文件路径
        :return:
        """
        return os.path.join(self.data_dir, "index", self.index_code, "day.csv")

    def _setup_dir_(self):
        """
        初始化数据目录
        :return:
        """
        os.makedirs(os.path.dirname(self.file_path()), exist_ok=True)

    def should_update(self) -> bool:
        """
        如果数据文件的最后修改日期, 早于最近的一个交易日, 则需要更新数据
        如果文件不存在, 直接返回 True
        :return:
        """
        if not os.path.exists(self.file_path()):
            return True

        self.prepare()

        return need_update_by_trade_date(self.dataframe, "date")

    def load(self) -> pd.DataFrame:
        if os.path.exists(self.file_path()):
            self.dataframe = pd.read_csv(
                filepath_or_buffer=self.file_path(),
                parse_dates=["date"],
                dtype={
                    "open": np.float64,
                    "high": np.float64,
                    "low": np.float64,
                    "close": np.float64,
                    "preclose": np.float64,
                    "volume": np.float64,
                    "amount": np.float64,
                    "pctChg": np.float64,
                },
            )
            self.dataframe.set_index(keys="date", drop=False, inplace=True)
            self.dataframe.sort_index(inplace=True)
        else:
            logging.warning(
                colorama.Fore.RED
                + "本地 [%s 日线] 数据文件不存在,请及时下载更新" % self.index_code
            )
            self.dataframe = pd.DataFrame()

        return self.dataframe

    def prepare(self):
        if self.dataframe is None:
            self.load()
        return self

    def start_date(self) -> date:
        """
        计算本次更新的起始日期
        :return:
        """
        if self.dataframe is None:
            self.load()

        if self.dataframe.empty:
            return self.base_date
        else:
            return self.dataframe.iloc[-1].loc["date"].date() + timedelta(days=1)

    def update(self):
        self._setup_dir_()
        self.prepare()

        if self.should_update():
            list_date = StockData().stock_basic.list_date_of(self.index_code)
            start_date: date = (
                max(self.start_date(), list_date)
                if list_date is not None
                else self.start_date()
            )
            end_date: date = start_date
            last_trade_day = StockData().trade_calendar.latest_trade_day()
            df_list: List[pd.DataFrame] = [self.dataframe]
            step_days = timedelta(days=50)

            while start_date <= last_trade_day:
                end_date = min(start_date + step_days, last_trade_day)
                rs = bs.query_history_k_data_plus(
                    self.index_code,
                    "date,code,open,high,low,close,preclose,volume,amount,pctChg",
                    start_date=str(start_date),
                    end_date=str(end_date),
                    frequency="d",
                )
                df_day = rs.get_data()
                if not df_day.empty:
                    df_day["date"] = pd.to_datetime(df_day["date"], format="%Y-%m-%d")
                    df_day["open"] = df_day["open"].astype(np.float64)
                    df_day["high"] = df_day["high"].astype(np.float64)
                    df_day["low"] = df_day["low"].astype(np.float64)
                    df_day["close"] = df_day["close"].astype(np.float64)
                    df_day["preclose"] = df_day["preclose"].astype(np.float64)
                    df_day.set_index(keys="date", drop=False, inplace=True)
                    logging.debug(
                        colorama.Fore.YELLOW
                        + "下载 %s 日线数据，从 %s 到 %s 共 %s 条"
                        % (self.index_code, start_date, end_date, df_day.shape[0])
                    )
                    df_list.append(df_day)
                start_date = end_date + timedelta(days=1)

            self.dataframe = pd.concat(df_list).drop_duplicates()
            self.dataframe.sort_index(inplace=True)

            self.dataframe.to_csv(path_or_buf=self.file_path(), index=False)
            logging.info(
                colorama.Fore.YELLOW
                + "%s 日线数据更新到: %s path: %s"
                % (self.index_code, str(end_date), self.file_path)
            )
        else:
            logging.info(colorama.Fore.BLUE + "%s 日线数据无须更新" % self.index_code)
