#!/usr/bin/env python3
"""
验证优化功能的简单脚本
"""

def main():
    print("🚀 A股数据下载优化功能验证")
    print("=" * 50)
    
    # 1. 验证基础库
    print("1. 验证基础库...")
    try:
        import baostock
        import psutil
        import colorama
        import pandas
        import numpy
        print("   ✅ 所有基础库导入成功")
    except ImportError as e:
        print(f"   ❌ 基础库导入失败: {e}")
        return False
    
    # 2. 验证优化模块
    print("2. 验证优化模块...")
    try:
        from update_config import DataUpdateConfig, PresetConfigs
        from performance_monitor import PerformanceMonitor
        from batch_downloader import BatchDownloader
        print("   ✅ 所有优化模块导入成功")
    except ImportError as e:
        print(f"   ❌ 优化模块导入失败: {e}")
        return False
    
    # 3. 验证配置功能
    print("3. 验证配置功能...")
    try:
        config = DataUpdateConfig()
        config.validate()
        fast_config = PresetConfigs.fast_daily_only()
        print("   ✅ 配置功能正常")
    except Exception as e:
        print(f"   ❌ 配置功能异常: {e}")
        return False
    
    # 4. 验证性能监控
    print("4. 验证性能监控...")
    try:
        from performance_monitor import check_system_resources
        resources = check_system_resources()
        print(f"   ✅ 性能监控正常: CPU {resources['cpu_count']}核")
    except Exception as e:
        print(f"   ❌ 性能监控异常: {e}")
        return False
    
    print("\n🎉 所有功能验证通过！")
    print("📖 使用说明:")
    print("   python quick_update.py --mode fast    # 快速模式")
    print("   python quick_update.py --mode full    # 完整模式")
    print("   python quick_update.py --help         # 查看帮助")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
