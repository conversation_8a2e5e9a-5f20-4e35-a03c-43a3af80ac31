{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600102/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600102/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600102/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600102/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000769/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000769/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000769/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000769/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000832/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000832/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000832/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000832/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000602/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000602/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000602/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000602/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000003/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000003/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000003/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000003/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600205/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600205/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600205/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600205/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000653/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000653/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000653/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000653/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600270/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600270/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600270/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600270/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600646/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600646/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600646/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600646/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600065/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600065/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600065/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600065/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000588/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000588/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000588/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000588/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600786/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600786/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600786/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600786/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.300372/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.300372/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.300372/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.300372/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600752/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600752/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600752/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600752/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600253/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600253/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600253/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600253/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600092/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600092/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600092/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600092/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000015/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000015/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000015/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000015/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600357/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600357/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600357/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600357/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000508/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000508/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000508/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000508/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600286/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600286/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600286/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600286/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600432/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600432/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600432/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600432/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000405/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000405/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000405/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000405/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000511/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000511/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000511/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000511/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000658/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000658/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000658/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000658/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000047/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000047/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000047/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000047/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000765/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000765/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000765/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000765/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000569/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000569/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000569/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000569/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600003/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600003/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600003/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600003/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600181/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600181/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600181/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600181/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000522/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000522/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000522/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000522/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000556/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000556/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000556/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000556/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000827/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000827/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000827/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000827/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600772/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600772/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600772/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600772/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000412/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000412/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000412/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000412/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600788/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600788/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600788/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600788/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600263/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600263/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600263/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600263/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600709/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600709/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600709/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600709/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600700/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600700/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600700/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600700/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600607/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600607/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600607/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600607/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000699/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000699/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000699/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000699/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000542/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000542/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000542/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000542/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000515/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000515/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000515/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000515/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600899/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600899/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600899/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600899/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000578/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000578/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000578/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000578/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600878/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600878/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600878/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600878/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000013/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000013/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000013/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000013/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.601268/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.601268/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.601268/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.601268/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600670/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600670/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600670/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600670/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600672/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600672/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600672/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600672/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000527/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000527/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000527/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000527/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000675/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000675/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000675/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000675/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000660/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000660/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000660/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000660/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000033/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000033/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000033/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000033/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000583/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000583/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000583/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000583/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600591/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600591/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600591/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600591/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600005/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600005/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600005/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600005/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600813/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600813/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600813/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600813/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600472/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600472/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600472/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600472/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000618/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000618/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000618/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000618/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600832/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600832/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600832/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600832/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000562/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000562/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000562/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000562/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000406/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000406/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000406/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000406/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600680/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600680/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600680/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600680/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000689/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000689/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000689/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000689/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000787/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000787/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000787/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000787/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000621/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000621/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000621/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000621/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000748/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000748/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000748/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000748/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600849/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600849/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600849/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600849/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000866/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000866/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000866/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000866/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000535/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000535/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000535/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000535/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600806/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600806/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600806/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600806/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600842/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600842/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600842/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600842/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600656/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600656/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600656/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600656/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.601299/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.601299/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.601299/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.601299/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600625/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600625/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600625/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600625/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600762/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600762/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600762/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600762/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000022/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000022/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000022/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000022/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600669/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600669/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600669/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600669/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000817/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000817/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000817/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000817/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600799/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600799/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600799/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600799/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000763/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000763/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000763/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000763/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600840/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600840/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600840/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600840/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000549/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000549/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000549/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000549/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600632/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600632/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600632/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600632/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600991/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600991/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600991/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600991/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000979/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000979/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000979/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000979/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000956/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000956/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000956/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000956/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000805/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000805/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000805/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000805/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600852/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600852/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600852/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600852/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000730/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000730/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000730/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000730/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600627/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600627/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600627/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600627/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600659/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600659/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600659/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600659/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000916/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000916/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000916/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000916/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600087/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600087/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600087/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600087/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600631/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600631/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600631/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600631/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000024/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000024/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000024/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000024/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.300186/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.300186/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.300186/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.300186/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000594/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000594/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000594/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sz.000594/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600553/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600553/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600553/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600553/5min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600296/15min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600296/30min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600296/60min.csv\n", "已删除空文件: /home/<USER>/Studios/600-codes/dataset/trade_data/stocks/sh.600296/5min.csv\n"]}], "source": ["import os\n", "import pandas as pd\n", "\n", "def remove_empty_csv(directory):\n", "    \"\"\"删除目录及子目录下所有空的CSV文件\"\"\"\n", "    for root, dirs, files in os.walk(directory):\n", "        for file in files:\n", "            if file.endswith('.csv'):\n", "                file_path = os.path.join(root, file)\n", "                try:\n", "                    df = pd.read_csv(file_path)\n", "                    if df.empty:\n", "                        os.remove(file_path)\n", "                        print(f\"已删除空文件: {file_path}\")\n", "                except pd.errors.EmptyDataError:\n", "                    os.remove(file_path) \n", "                    print(f\"已删除空文件: {file_path}\")\n", "                except Exception as e:\n", "                    print(f\"处理文件 {file_path} 时出错: {str(e)}\")\n", "\n", "# 指定要清理的目录路径\n", "data_dir = \"/home/<USER>/Studios/600-codes/dataset/trade_data/stocks\"\n", "remove_empty_csv(data_dir)\n"]}], "metadata": {"kernelspec": {"display_name": "ml4t", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 2}