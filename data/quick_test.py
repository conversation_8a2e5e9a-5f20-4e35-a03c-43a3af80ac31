#!/usr/bin/env python3
"""
快速测试脚本 - 验证核心优化功能
"""

def test_core_modules():
    """测试核心模块"""
    print("🔍 测试核心优化模块...")
    
    # 测试配置模块
    try:
        from update_config import DataUpdateConfig, PresetConfigs
        config = DataUpdateConfig()
        config.validate()
        print("✅ 配置模块正常")
    except Exception as e:
        print(f"❌ 配置模块失败: {e}")
        return False
    
    # 测试性能监控模块
    try:
        from performance_monitor import PerformanceMonitor, check_system_resources
        resources = check_system_resources()
        print(f"✅ 性能监控模块正常 (CPU: {resources['cpu_count']}核)")
    except Exception as e:
        print(f"❌ 性能监控模块失败: {e}")
        return False
    
    # 测试批量下载模块
    try:
        from batch_downloader import BatchDownloader
        config = DataUpdateConfig()
        downloader = BatchDownloader(config)
        print("✅ 批量下载模块正常")
    except Exception as e:
        print(f"❌ 批量下载模块失败: {e}")
        return False
    
    return True

def main():
    print("🚀 A股数据下载优化 - 快速测试")
    print("=" * 40)
    
    if test_core_modules():
        print("\n🎉 核心优化功能测试通过！")
        print("\n📖 使用方法:")
        print("  python quick_update.py --mode fast")
        print("  python quick_update.py --help")
        return True
    else:
        print("\n❌ 测试失败，请检查模块安装")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
