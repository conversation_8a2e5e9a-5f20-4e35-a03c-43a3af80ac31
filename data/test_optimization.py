#!/usr/bin/env python3
"""
测试数据下载优化功能
验证各个模块是否正常工作
"""
import sys
import os
import tempfile
import shutil
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from update_config import DataUpdateConfig, PresetConfigs
        from performance_monitor import PerformanceMonitor, check_system_resources
        from batch_downloader import BatchDownloader
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def test_config():
    """测试配置功能"""
    print("测试配置功能...")
    
    try:
        from update_config import DataUpdateConfig, PresetConfigs
        
        # 测试默认配置
        config = DataUpdateConfig()
        config.validate()
        
        # 测试预设配置
        fast_config = PresetConfigs.fast_daily_only()
        fast_config.validate()
        
        full_config = PresetConfigs.full_data()
        full_config.validate()
        
        conservative_config = PresetConfigs.conservative()
        conservative_config.validate()
        
        print("✅ 配置功能测试通过")
        return True
    except Exception as e:
        print(f"❌ 配置功能测试失败: {e}")
        return False


def test_performance_monitor():
    """测试性能监控"""
    print("测试性能监控...")
    
    try:
        from performance_monitor import PerformanceMonitor, check_system_resources
        
        # 测试系统资源检查
        resources = check_system_resources()
        assert 'cpu_count' in resources
        assert 'memory_total_gb' in resources
        
        # 测试性能监控器
        monitor = PerformanceMonitor("测试")
        monitor.start_monitoring(10)
        
        for i in range(10):
            monitor.update_progress(success=(i % 2 == 0))
        
        monitor.finish_monitoring()
        status = monitor.get_current_status()
        assert status['completed_tasks'] == 10
        
        print("✅ 性能监控测试通过")
        return True
    except Exception as e:
        print(f"❌ 性能监控测试失败: {e}")
        return False


def test_batch_downloader():
    """测试批量下载器"""
    print("测试批量下载器...")
    
    try:
        from batch_downloader import BatchDownloader
        from update_config import DataUpdateConfig
        
        config = DataUpdateConfig()
        config.max_workers = 1  # 减少并发数用于测试
        
        downloader = BatchDownloader(config)
        
        # 测试任务创建
        stock_codes = ['sh.600000', 'sz.000001']
        tasks = downloader.create_stock_tasks(stock_codes, '/tmp/test')
        assert len(tasks) == 2
        
        index_codes = ['sh.000001']
        index_tasks = downloader.create_index_tasks(index_codes, '/tmp/test')
        assert len(index_tasks) == 1
        
        print("✅ 批量下载器测试通过")
        return True
    except Exception as e:
        print(f"❌ 批量下载器测试失败: {e}")
        return False


def test_data_structure():
    """测试数据结构"""
    print("测试数据结构...")
    
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            from stock.stock_data import StockData
            
            # 测试数据目录设置
            StockData().setup(data_dir=temp_dir)
            assert StockData().data_dir == temp_dir
            
            print("✅ 数据结构测试通过")
            return True
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
            
    except Exception as e:
        print(f"❌ 数据结构测试失败: {e}")
        return False


def test_logging():
    """测试日志功能"""
    print("测试日志功能...")
    
    try:
        # 设置临时日志文件
        temp_log = tempfile.mktemp(suffix='.log')
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.FileHandler(temp_log)]
        )
        
        logging.info("测试日志消息")
        
        # 检查日志文件是否创建
        assert os.path.exists(temp_log)
        
        # 清理
        os.unlink(temp_log)
        
        print("✅ 日志功能测试通过")
        return True
    except Exception as e:
        print(f"❌ 日志功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 50)
    print("A股数据下载优化功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_performance_monitor,
        test_batch_downloader,
        test_data_structure,
        test_logging
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print("测试结果汇总:")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有测试通过！优化功能正常工作。")
        return 0
    else:
        print(f"\n⚠️  有 {failed} 个测试失败，请检查相关模块。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
