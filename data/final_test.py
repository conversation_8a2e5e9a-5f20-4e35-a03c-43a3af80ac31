#!/usr/bin/env python3
"""
最终测试脚本 - 验证所有优化功能
"""
import sys
import os
import tempfile
import shutil

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_all_imports():
    """测试所有必要的导入"""
    print("🔍 测试模块导入...")
    
    imports = [
        ("baostock", "数据源库"),
        ("psutil", "系统监控库"),
        ("colorama", "彩色输出库"),
        ("pandas", "数据处理库"),
        ("numpy", "数值计算库"),
    ]
    
    success_count = 0
    for module_name, description in imports:
        try:
            __import__(module_name)
            print(f"  ✅ {module_name} ({description}) - 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {module_name} ({description}) - 导入失败: {e}")
    
    print(f"📊 导入测试结果: {success_count}/{len(imports)} 成功")
    return success_count == len(imports)

def test_optimization_modules():
    """测试优化模块"""
    print("\n🔍 测试优化模块...")
    
    modules = [
        ("update_config", "配置管理模块"),
        ("performance_monitor", "性能监控模块"),
        ("batch_downloader", "批量下载模块"),
    ]
    
    success_count = 0
    for module_name, description in modules:
        try:
            __import__(module_name)
            print(f"  ✅ {module_name} ({description}) - 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {module_name} ({description}) - 导入失败: {e}")
    
    print(f"📊 优化模块测试结果: {success_count}/{len(modules)} 成功")
    return success_count == len(modules)

def test_config_functionality():
    """测试配置功能"""
    print("\n🔍 测试配置功能...")
    
    try:
        from update_config import DataUpdateConfig, PresetConfigs
        
        # 测试默认配置
        config = DataUpdateConfig()
        config.validate()
        print("  ✅ 默认配置创建和验证成功")
        
        # 测试预设配置
        fast_config = PresetConfigs.fast_daily_only()
        fast_config.validate()
        print("  ✅ 快速模式配置测试成功")
        
        full_config = PresetConfigs.full_data()
        full_config.validate()
        print("  ✅ 完整模式配置测试成功")
        
        conservative_config = PresetConfigs.conservative()
        conservative_config.validate()
        print("  ✅ 保守模式配置测试成功")
        
        # 测试配置属性
        enabled_types = config.get_enabled_data_types()
        print(f"  ✅ 配置属性测试成功: {enabled_types}")
        
        return True
    except Exception as e:
        print(f"  ❌ 配置功能测试失败: {e}")
        return False

def test_performance_monitor():
    """测试性能监控功能"""
    print("\n🔍 测试性能监控功能...")
    
    try:
        from performance_monitor import PerformanceMonitor, check_system_resources
        
        # 测试系统资源检查
        resources = check_system_resources()
        print(f"  ✅ 系统资源检查成功: CPU {resources['cpu_count']}核, "
              f"内存 {resources['memory_total_gb']:.1f}GB")
        
        # 测试性能监控器
        monitor = PerformanceMonitor("测试任务")
        monitor.start_monitoring(10)
        
        # 模拟任务执行
        for i in range(10):
            success = (i % 3 != 0)  # 约67%成功率
            monitor.update_progress(success=success)
        
        monitor.finish_monitoring()
        status = monitor.get_current_status()
        print(f"  ✅ 性能监控器测试成功: 完成率 {status['progress_percent']:.1f}%, "
              f"成功率 {status['success_rate']:.1f}%")
        
        return True
    except Exception as e:
        print(f"  ❌ 性能监控功能测试失败: {e}")
        return False

def test_batch_downloader():
    """测试批量下载器功能"""
    print("\n🔍 测试批量下载器功能...")
    
    try:
        from batch_downloader import BatchDownloader
        from update_config import DataUpdateConfig
        
        # 创建测试配置
        config = DataUpdateConfig()
        config.max_workers = 2
        config.enable_daily = True
        
        # 创建下载器
        downloader = BatchDownloader(config)
        print("  ✅ 批量下载器创建成功")
        
        # 测试任务创建
        stock_codes = ['sh.600000', 'sz.000001', 'sh.600036']
        tasks = downloader.create_stock_tasks(stock_codes, '/tmp/test_data')
        print(f"  ✅ 股票任务创建成功: {len(tasks)} 个任务")
        
        index_codes = ['sh.000001', 'sz.399001']
        index_tasks = downloader.create_index_tasks(index_codes, '/tmp/test_data')
        print(f"  ✅ 指数任务创建成功: {len(index_tasks)} 个任务")
        
        return True
    except Exception as e:
        print(f"  ❌ 批量下载器功能测试失败: {e}")
        return False

def test_data_structure():
    """测试数据结构"""
    print("\n🔍 测试数据结构...")
    
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            from stock.stock_data import StockData
            
            # 测试数据目录设置
            StockData().setup(data_dir=temp_dir)
            assert StockData().data_dir == temp_dir
            print(f"  ✅ 数据目录设置成功: {temp_dir}")
            
            return True
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
            
    except Exception as e:
        print(f"  ❌ 数据结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 A股数据下载优化功能 - 最终测试")
    print("=" * 60)
    
    tests = [
        ("基础库导入", test_all_imports),
        ("优化模块导入", test_optimization_modules),
        ("配置功能", test_config_functionality),
        ("性能监控", test_performance_monitor),
        ("批量下载器", test_batch_downloader),
        ("数据结构", test_data_structure),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 测试通过")
            else:
                failed += 1
                print(f"❌ {test_name} - 测试失败")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} - 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 最终测试结果:")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 恭喜！所有测试通过！")
        print("🚀 A股数据下载优化功能已准备就绪，可以开始使用。")
        print("\n📖 使用方法:")
        print("   python quick_update.py --mode fast")
        return 0
    else:
        print(f"\n⚠️  有 {failed} 个测试失败。")
        print("💡 建议检查虚拟环境和依赖安装。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
