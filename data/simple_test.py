#!/usr/bin/env python3
"""
简化的测试脚本
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        import baostock
        print("✅ baostock 导入成功")
    except ImportError as e:
        print(f"❌ baostock 导入失败: {e}")
        return False
    
    try:
        import psutil
        print("✅ psutil 导入成功")
    except ImportError as e:
        print(f"❌ psutil 导入失败: {e}")
        return False
    
    try:
        import colorama
        print("✅ colorama 导入成功")
    except ImportError as e:
        print(f"❌ colorama 导入失败: {e}")
        return False
    
    return True

def test_config_module():
    """测试配置模块"""
    print("测试配置模块...")
    
    try:
        from update_config import DataUpdateConfig, PresetConfigs
        
        # 测试默认配置
        config = DataUpdateConfig()
        config.validate()
        print("✅ 默认配置测试通过")
        
        # 测试预设配置
        fast_config = PresetConfigs.fast_daily_only()
        fast_config.validate()
        print("✅ 快速模式配置测试通过")
        
        return True
    except Exception as e:
        print(f"❌ 配置模块测试失败: {e}")
        return False

def test_performance_monitor():
    """测试性能监控模块"""
    print("测试性能监控模块...")
    
    try:
        from performance_monitor import PerformanceMonitor, check_system_resources
        
        # 测试系统资源检查
        resources = check_system_resources()
        print(f"✅ 系统资源检查成功: CPU {resources['cpu_count']}核")
        
        # 测试性能监控器
        monitor = PerformanceMonitor("测试")
        monitor.start_monitoring(5)
        
        for i in range(5):
            monitor.update_progress(success=True)
        
        monitor.finish_monitoring()
        print("✅ 性能监控器测试通过")
        
        return True
    except Exception as e:
        print(f"❌ 性能监控模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("简化版数据下载优化功能测试")
    print("=" * 50)
    
    tests = [
        test_basic_imports,
        test_config_module,
        test_performance_monitor,
    ]
    
    passed = 0
    failed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print("测试结果汇总:")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📊 成功率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有测试通过！优化功能正常工作。")
        return 0
    else:
        print(f"\n⚠️  有 {failed} 个测试失败，请检查相关模块。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
