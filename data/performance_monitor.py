"""
性能监控和进度显示工具
"""
import time
import psutil
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta


@dataclass
class TaskStats:
    """任务统计信息"""
    total_tasks: int = 0
    completed_tasks: int = 0
    success_tasks: int = 0
    failed_tasks: int = 0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    
    @property
    def progress_percent(self) -> float:
        """完成百分比"""
        if self.total_tasks == 0:
            return 0.0
        return (self.completed_tasks / self.total_tasks) * 100
    
    @property
    def elapsed_time(self) -> float:
        """已用时间（秒）"""
        if self.start_time is None:
            return 0.0
        end = self.end_time or time.time()
        return end - self.start_time
    
    @property
    def estimated_remaining_time(self) -> float:
        """预计剩余时间（秒）"""
        if self.completed_tasks == 0 or self.start_time is None:
            return 0.0
        
        avg_time_per_task = self.elapsed_time / self.completed_tasks
        remaining_tasks = self.total_tasks - self.completed_tasks
        return remaining_tasks * avg_time_per_task
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.completed_tasks == 0:
            return 0.0
        return (self.success_tasks / self.completed_tasks) * 100


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, name: str = "DataUpdate"):
        self.name = name
        self.stats = TaskStats()
        self.process = psutil.Process()
        self.start_memory = None
        self.start_cpu_time = None
        
    def start_monitoring(self, total_tasks: int):
        """开始监控"""
        self.stats.total_tasks = total_tasks
        self.stats.start_time = time.time()
        self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        self.start_cpu_time = self.process.cpu_times()
        
        logging.info(f"开始监控 {self.name}，总任务数: {total_tasks}")
        logging.info(f"初始内存使用: {self.start_memory:.1f} MB")
    
    def update_progress(self, success: bool = True):
        """更新进度"""
        self.stats.completed_tasks += 1
        if success:
            self.stats.success_tasks += 1
        else:
            self.stats.failed_tasks += 1
    
    def get_current_status(self) -> Dict:
        """获取当前状态"""
        current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        cpu_percent = self.process.cpu_percent()
        
        return {
            'progress_percent': self.stats.progress_percent,
            'completed_tasks': self.stats.completed_tasks,
            'total_tasks': self.stats.total_tasks,
            'success_rate': self.stats.success_rate,
            'elapsed_time_minutes': self.stats.elapsed_time / 60,
            'estimated_remaining_minutes': self.stats.estimated_remaining_time / 60,
            'current_memory_mb': current_memory,
            'memory_increase_mb': current_memory - (self.start_memory or 0),
            'cpu_percent': cpu_percent
        }
    
    def log_progress(self, interval: int = 100):
        """记录进度日志"""
        if self.stats.completed_tasks % interval == 0:
            status = self.get_current_status()
            logging.info(
                f"进度: {status['completed_tasks']}/{status['total_tasks']} "
                f"({status['progress_percent']:.1f}%), "
                f"成功率: {status['success_rate']:.1f}%, "
                f"已用时间: {status['elapsed_time_minutes']:.1f}分钟, "
                f"预计剩余: {status['estimated_remaining_minutes']:.1f}分钟, "
                f"内存: {status['current_memory_mb']:.1f}MB"
            )
    
    def finish_monitoring(self):
        """结束监控"""
        self.stats.end_time = time.time()
        final_status = self.get_current_status()
        
        logging.info(f"{self.name} 监控结束")
        logging.info(f"总任务数: {self.stats.total_tasks}")
        logging.info(f"成功: {self.stats.success_tasks}, 失败: {self.stats.failed_tasks}")
        logging.info(f"成功率: {final_status['success_rate']:.1f}%")
        logging.info(f"总耗时: {final_status['elapsed_time_minutes']:.1f}分钟")
        logging.info(f"最终内存使用: {final_status['current_memory_mb']:.1f}MB")
        logging.info(f"内存增长: {final_status['memory_increase_mb']:.1f}MB")


class ProgressBar:
    """简单的进度条"""
    
    def __init__(self, total: int, width: int = 50):
        self.total = total
        self.width = width
        self.current = 0
        self.start_time = time.time()
    
    def update(self, count: int = 1):
        """更新进度"""
        self.current += count
        self._display()
    
    def _display(self):
        """显示进度条"""
        if self.total == 0:
            return
            
        percent = self.current / self.total
        filled = int(self.width * percent)
        bar = '█' * filled + '░' * (self.width - filled)
        
        elapsed = time.time() - self.start_time
        if self.current > 0:
            eta = (elapsed / self.current) * (self.total - self.current)
            eta_str = str(timedelta(seconds=int(eta)))
        else:
            eta_str = "未知"
        
        print(f'\r进度: |{bar}| {self.current}/{self.total} '
              f'({percent*100:.1f}%) ETA: {eta_str}', end='', flush=True)
    
    def finish(self):
        """完成进度条"""
        print()  # 换行


def check_system_resources() -> Dict:
    """检查系统资源"""
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    cpu_count = psutil.cpu_count()
    
    return {
        'memory_total_gb': memory.total / 1024 / 1024 / 1024,
        'memory_available_gb': memory.available / 1024 / 1024 / 1024,
        'memory_percent': memory.percent,
        'disk_total_gb': disk.total / 1024 / 1024 / 1024,
        'disk_free_gb': disk.free / 1024 / 1024 / 1024,
        'disk_percent': (disk.used / disk.total) * 100,
        'cpu_count': cpu_count,
        'cpu_percent': psutil.cpu_percent(interval=1)
    }


def log_system_info():
    """记录系统信息"""
    resources = check_system_resources()
    logging.info("系统资源状态:")
    logging.info(f"  CPU: {resources['cpu_count']}核, 使用率: {resources['cpu_percent']:.1f}%")
    logging.info(f"  内存: {resources['memory_available_gb']:.1f}GB可用 / {resources['memory_total_gb']:.1f}GB总计 "
                f"(使用率: {resources['memory_percent']:.1f}%)")
    logging.info(f"  磁盘: {resources['disk_free_gb']:.1f}GB可用 / {resources['disk_total_gb']:.1f}GB总计 "
                f"(使用率: {resources['disk_percent']:.1f}%)")


if __name__ == "__main__":
    # 测试性能监控器
    monitor = PerformanceMonitor("测试任务")
    monitor.start_monitoring(100)
    
    # 模拟任务执行
    for i in range(100):
        time.sleep(0.01)  # 模拟工作
        monitor.update_progress(success=(i % 10 != 0))  # 90%成功率
        monitor.log_progress(interval=20)
    
    monitor.finish_monitoring()
