import os
import pandas as pd
import numpy as np
from typing import Union
from datetime import date, datetime

# from stock_data import StockData


def need_update(fpath: str, outdate_days: int) -> bool:
    """
    判断文件是否需要更新
    :param fpath: 文件路径
    :param outdate_days: 过期天数阈值
    :return: 如果文件不存在或者过期则返回True,否则返回False
    """
    # 如果文件不存在,需要更新
    if not os.path.exists(fpath):
        return True
    else:
        # 获取文件最后修改时间
        modify_date = date.fromtimestamp(os.stat(fpath).st_mtime)
        # 获取当前日期
        today = date.today()
        # 计算文件最后修改距今的天数
        diff_days = (today - modify_date).days
        # 如果超过过期天数阈值,需要更新
        if diff_days > outdate_days:
            return True
        else:
            return False


def need_update_by_trade_date(df: pd.DataFrame, column_name: str) -> bool:
    """
    根据交易日期判断数据是否需要更新

    参数:
        df: 待检查的数据框
        column_name: 日期列的名称

    返回:
        bool: 如果数据框为空或者最后一条数据的日期早于最新交易日,返回True表示需要更新
    """
    if df.empty:
        return True
    else:
        from stock.stock_data import StockData

        return (
            df.iloc[-1].loc[column_name].date()  # 获取数据最后一行的日期
            < StockData().trade_calendar.latest_trade_day()  # 与最新交易日比较
        )


def mtime_of_file(fpath: str) -> date:
    """
    获取文件的最后修改日期
    :param fpath:
    :return:
    """
    return date.fromtimestamp(os.stat(fpath).st_mtime)


def to_datetime64(x: Union[str, pd.Timestamp, date]) -> Union[np.datetime64, None]:
    """
    将不同格式的日期时间转换为numpy的datetime64类型

    参数:
        x: 输入的日期时间值,可以是字符串、Timestamp或date类型
            支持的字符串格式:
            - YYYYMM (6位)
            - YYYYMMDD (8位)
            - YYYY-MM-DD (10位)
            - YYYY-MM-DD HH:MM:SS (19位)

    返回:
        numpy.datetime64类型的日期时间值
        如果输入无效则返回None
    """
    if x is None:
        return None
    if x is date:
        return x
    if x is pd.Timestamp:
        return pd.to_datetime(datetime(x.year, x.month, x.day))
    if len(x) == 6:
        return pd.to_datetime(x, format="%Y%m")
    elif len(x) == 8:
        return pd.to_datetime(x, format="%Y%m%d")
    elif len(x) == 10:
        return pd.to_datetime(x, format="%Y-%m-%d")
    elif len(x) == 19:
        return pd.to_datetime(x, format="%Y-%m-%d %H:%M:%S")
    else:
        return None
