import torch
import torch.nn as nn
import torch.optim as optim
import yaml
from tqdm import tqdm
from data_loader.dataset import create_dataloaders
from models.resnet import get_resnet_model
from utils.visualization import plot_training_progress
from utils.logger import Logger


def train(config):
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 创建数据加载器
    train_loader, val_loader, _ = create_dataloaders(config)

    # 获取模型
    model = get_resnet_model(config).to(device)

    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.SGD(
        model.parameters(),
        lr=config["training"]["learning_rate"],
        momentum=config["training"]["momentum"],
        weight_decay=config["training"]["weight_decay"],
    )

    # 创建logger
    logger = Logger("logs")

    # 训练循环
    train_losses, val_losses = [], []
    train_accuracies, val_accuracies = [], []

    for epoch in range(config["training"]["epochs"]):
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0

        for batch_idx, (inputs, targets) in enumerate(tqdm(train_loader)):
            inputs, targets = inputs.to(device), targets.to(device)
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()
            _, predicted = outputs.max(1)
            train_total += targets.size(0)
            train_correct += predicted.eq(targets).sum().item()

            if batch_idx % config["visualization"]["log_interval"] == 0:
                logger.log_scalar(
                    "train/loss", loss.item(), epoch * len(train_loader) + batch_idx
                )

        train_loss /= len(train_loader)
        train_accuracy = 100.0 * train_correct / train_total
        train_losses.append(train_loss)
        train_accuracies.append(train_accuracy)

        # 验证
        model.eval()
        val_loss = 0
        val_correct = 0
        val_total = 0

        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs, targets = inputs.to(device), targets.to(device)
                outputs = model(inputs)
                loss = criterion(outputs, targets)

                val_loss += loss.item()
                _, predicted = outputs.max(1)
                val_total += targets.size(0)
                val_correct += predicted.eq(targets).sum().item()

        val_loss /= len(val_loader)
        val_accuracy = 100.0 * val_correct / val_total
        val_losses.append(val_loss)
        val_accuracies.append(val_accuracy)

        logger.log_scalar("val/loss", val_loss, epoch)
        logger.log_scalar("val/accuracy", val_accuracy, epoch)

        print(
            f"Epoch {epoch+1}/{config['training']['epochs']}, "
            f"Train Loss: {train_loss:.4f}, Train Acc: {train_accuracy:.2f}%, "
            f"Val Loss: {val_loss:.4f}, Val Acc: {val_accuracy:.2f}%"
        )

    # 绘制训练进度图
    plot_training_progress(train_losses, val_losses, train_accuracies, val_accuracies)

    # 保存模型
    torch.save(model.state_dict(), "model.pth")

    logger.close()


if __name__ == "__main__":
    with open("configs/config.yaml", "r") as f:
        config = yaml.safe_load(f)
    train(config)
