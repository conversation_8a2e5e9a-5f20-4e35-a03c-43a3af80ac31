import torch
import yaml
from tqdm import tqdm
from sklearn.metrics import confusion_matrix
import numpy as np
from data_loader.dataset import create_dataloaders
from models.resnet import get_resnet_model
from utils.visualization import plot_confusion_matrix


def evaluate(config):
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 创建数据加载器
    _, _, test_loader = create_dataloaders(config)

    # 获取模型
    model = get_resnet_model(config).to(device)
    model.load_state_dict(torch.load("model.pth"))
    model.eval()

    all_predictions = []
    all_targets = []

    with torch.no_grad():
        for inputs, targets in tqdm(test_loader):
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = model(inputs)
            _, predicted = outputs.max(1)

            all_predictions.extend(predicted.cpu().numpy())
            all_targets.extend(targets.cpu().numpy())

    # 计算准确率
    accuracy = 100 * np.mean(np.array(all_predictions) == np.array(all_targets))
    print(f"Test Accuracy: {accuracy:.2f}%")

    # 计算并绘制混淆矩阵
    cm = confusion_matrix(all_targets, all_predictions)
    class_names = [str(i) for i in range(config["model"]["num_classes"])]
    plot_confusion_matrix(cm, class_names)


if __name__ == "__main__":
    with open("configs/config.yaml", "r") as f:
        config = yaml.safe_load(f)
    evaluate(config)
