#!/bin/bash
# 激活虚拟环境的脚本
echo "正在设置代理和激活虚拟环境..."

# 设置代理（如果clash在运行）
if netstat -tlnp 2>/dev/null | grep -q ":7890.*LISTEN"; then
    export http_proxy=http://127.0.0.1:7890
    export https_proxy=http://127.0.0.1:7890
    echo "✓ 代理已设置: $http_proxy"
else
    echo "⚠ 未检测到代理服务，如果网络有问题请手动启动代理"
fi

# 激活虚拟环境
source .venv/bin/activate
echo "✓ 虚拟环境已激活！"
echo "Python版本: $(python --version)"
echo "虚拟环境路径: $VIRTUAL_ENV"
echo ""
echo "已安装的主要依赖："
echo "  - streamlit (Web应用框架)"
echo "  - pandas (数据处理)"
echo "  - numpy (数值计算)"
echo "  - yfinance (金融数据)"
echo "  - plotly (数据可视化)"
echo "  - akshare (中国金融数据)"
echo ""
echo "要退出虚拟环境，请运行："
echo "  deactivate"
