from datetime import datetime
from data import ods
import baostock as bs


class dailyPipeline:
    def __init__(self): ...

    # 更新数据
    def pipeline_get_data():
        ods_data = ods.odsData()  # 初始化 odsData 实例
        bs.login()
        # 获取交易日历
        ods_data.ods_baostock(data_type="交易日")
        # 获取行业分类
        ods_data.ods_baostock(data_type="行业分类")
        # 获取证券资料
        ods_data.ods_baostock(data_type="证券资料")
        # 获取证券代码
        ods_data.ods_baostock(data_type="证券代码")

        # 获取增量数据，默认数据从2000-01-01开始

    # 特征工程
    def pipeline_feature_engineering_fredict(date_start, date_end="2029-01-01"): ...

    def pipeline_stock_pick(): ...

    def wechat(): ...


if __name__ == "__main__":
    today = datetime.now().strftime("%F")
    # 创建一个实例
    daily_pipeline = dailyPipeline()
    # 更新数据
    daily_pipeline.pipeline_get_data()
    # 特征工程
    daily_pipeline.pipeline_feature_engineering_fredict(today)
