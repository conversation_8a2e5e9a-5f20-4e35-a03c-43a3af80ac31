import os
import sys
import socket
import subprocess
import time
from pathlib import Path


def find_free_port(start_port=8501, max_port=8599):
    """查找可用端口"""
    for port in range(start_port, max_port + 1):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            try:
                # 设置端口重用
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                sock.bind(("0.0.0.0", port))
                return port
            except OSError:
                continue
    raise RuntimeError("没有找到可用的端口")


def kill_process_on_port(port):
    """杀死占用指定端口的进程"""
    try:
        # 对于 Linux/Unix 系统
        subprocess.run(["fuser", "-k", f"{port}/tcp"], check=False)
        time.sleep(1)  # 等待进程结束
    except Exception:
        pass


def main():
    """主函数"""
    # 获取项目根目录
    root_dir = Path(__file__).parent

    # 添加项目根目录到 Python 路径
    sys.path.append(str(root_dir))

    # 构建 Streamlit 应用路径
    app_path = root_dir / "ui" / "streamlit_app.py"

    try:
        # 先尝试杀死可能占用 8501 端口的进程
        kill_process_on_port(8501)

        # 查找可用端口
        port = find_free_port(8501, 8510)
        print(f"使用端口: {port}")

        # 获取本机IP地址
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"应用运行在: http://{local_ip}:{port}")

        # 运行 Streamlit 应用
        process = subprocess.Popen(
            [
                "streamlit",
                "run",
                str(app_path),
                "--server.address",
                "0.0.0.0",
                "--server.port",
                str(port),
                "--browser.serverAddress",
                local_ip,
                "--browser.serverPort",
                str(port),
            ]
        )

        # 等待进程结束或用户中断
        process.wait()

    except KeyboardInterrupt:
        print("\n正在停止应用...")
        if "process" in locals():
            process.terminate()
            process.wait(timeout=5)
        print("应用已停止运行")
        sys.exit(0)
    except Exception as e:
        print(f"发生错误: {e}")
        if "process" in locals():
            process.terminate()
            process.wait(timeout=5)
        sys.exit(1)


if __name__ == "__main__":
    main()
