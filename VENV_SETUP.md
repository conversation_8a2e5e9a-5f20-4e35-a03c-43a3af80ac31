# Python虚拟环境设置说明

## 环境信息
- **Python版本**: 3.12.7
- **包管理器**: uv (超快的Python包管理器)
- **虚拟环境路径**: `.venv/`

## 快速开始

### 1. 激活虚拟环境
```bash
# 使用提供的脚本（推荐）
./activate_env.sh

# 或者手动激活
source .venv/bin/activate
```

### 2. 验证环境
激活后，你应该看到：
- 命令提示符前缀显示 `(special-adventure)`
- Python版本为 3.12.7
- 所有依赖已安装

### 3. 退出虚拟环境
```bash
deactivate
```

## 已安装的依赖

| 包名 | 版本 | 用途 |
|------|------|------|
| streamlit | 1.29.0 | Web应用框架 |
| pandas | 2.1.4 | 数据处理和分析 |
| numpy | 1.26.2 | 数值计算 |
| yfinance | 0.2.33 | 获取金融数据 |
| plotly | 5.18.0 | 交互式数据可视化 |
| akshare | 1.16.96 | 中国金融数据接口 |

## 网络代理设置

激活脚本会自动检测并设置代理：
- 如果检测到clash代理（端口7890），会自动设置
- 如果没有代理，会显示警告信息

手动设置代理：
```bash
export http_proxy=http://127.0.0.1:7890
export https_proxy=http://127.0.0.1:7890
```

## 管理依赖

### 安装新包
```bash
# 激活环境后
uv pip install 包名

# 或者添加到requirements.txt后
uv pip install -r requirements.txt
```

### 查看已安装包
```bash
uv pip list
```

### 更新包
```bash
uv pip install --upgrade 包名
```

## 故障排除

### 网络问题
如果遇到网络连接问题：
1. 确保代理服务正在运行
2. 手动设置代理环境变量
3. 或使用国内镜像源

### 依赖冲突
如果遇到依赖冲突：
1. 检查requirements.txt中的版本要求
2. 使用 `uv pip install --force-reinstall` 重新安装
3. 考虑更新到兼容的版本

## 项目运行

激活虚拟环境后，你可以运行项目中的脚本：
```bash
# 激活环境
./activate_env.sh

# 运行主程序
python main.py

# 或运行Streamlit应用
streamlit run app.py
```
