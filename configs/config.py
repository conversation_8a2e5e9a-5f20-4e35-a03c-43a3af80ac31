"""
Configuration parameters for the quantitative trading system
"""

import datetime

# 回测参数
LOOK_BACK_PERIOD = 60
LOOK_FORWARD_PERIOD = 2
TRAIN_SPLIT_PCT = 0.7

# 回测数据范围
START_DATE = "2019-01-01"
END_DATE = "2024-12-20"

# 模型参数
LIGHTGBM_PARAMS = {
    "objective": "multiclass",
    "num_class": 3,
    "boosting_type": "gbdt",
    "learning_rate": 0.01,
    "num_leaves": 91,
    "max_depth": 3,
    "n_estimators": 5000,
    "subsample": 1.0,
    "colsample_bytree": 0.5,
    "reg_alpha": 0.2,
    "reg_lambda": 0.2,
    "random_state": 2021,
    "n_jobs": -1,
    "verbose": -1,
}

# 组合大小
PORTFOLIO_SIZE = 5  # Number of stocks to hold
REBALANCE_PERIOD = 2  # Days between rebalancing

# File paths
MODEL_SAVE_PATH = "models/lightgbm_model.pkl"
FEATURE_CACHE_PATH = "data/features_cache.pkl"
